# MCP Client Configuration Guide

This guide provides detailed instructions for configuring various MCP clients to connect to the String Processor MCP Server.

## 🎯 Server Information

- **Server Name**: String Processor MCP Server
- **Capabilities**: String processing, session management, batch operations
- **Transport Modes**: STDIO (local) and HTTP (remote)
- **Port**: 8003 (HTTP mode)

## 🔧 Configuration by Client

### 1. <PERSON> Desktop

#### STDIO Mode (Recommended)
**Configuration File Locations:**
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/<PERSON>/claude_desktop_config.json`

**Configuration:**
```json
{
  "mcpServers": {
    "string-processor": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Desktop\\杂项\\mcp\\string-processor-server\\string_processor_server.py"
      ],
      "env": {
        "CONDA_DEFAULT_ENV": "mcp-dev",
        "PATH": "C:\\Users\\<USER>\\anaconda3\\envs\\mcp-dev\\Scripts;C:\\Users\\<USER>\\anaconda3\\envs\\mcp-dev"
      }
    }
  }
}
```

**Setup Steps:**
1. Ensure the mcp-dev conda environment is properly set up
2. Update the file path to match your actual installation
3. Save the configuration file
4. Restart Claude Desktop
5. When starting the server, select option 1 (STDIO mode)

### 2. Cursor

#### STDIO Mode
**Configuration Location:** Cursor Settings → MCP Servers

```json
{
  "mcpServers": {
    "string-processor": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Desktop\\杂项\\mcp\\string-processor-server\\string_processor_server.py"
      ],
      "env": {
        "CONDA_DEFAULT_ENV": "mcp-dev",
        "PATH": "C:\\Users\\<USER>\\anaconda3\\envs\\mcp-dev\\Scripts;C:\\Users\\<USER>\\anaconda3\\envs\\mcp-dev;C:\\Windows\\System32"
      }
    }
  }
}
```

### 3. HTTP Mode (Remote Access)

For clients that support HTTP MCP connections:

```json
{
  "mcpServers": {
    "string-processor-remote": {
      "url": "http://your-server-ip:8003/mcp",
      "headers": {
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      "timeout": 30000
    }
  }
}
```

**Finding Your Server IP:**
```bash
# Windows
ipconfig | findstr IPv4

# Linux/macOS
ip addr show | grep inet
# or
ifconfig | grep inet
```

## 🚀 Setup Instructions

### Step 1: Prepare Environment
```bash
# Activate conda environment
conda activate mcp-dev

# Verify MCP installation
python -c "import mcp; print('MCP is available')"

# Install HTTP dependencies (for HTTP mode)
pip install fastapi uvicorn
```

### Step 2: Start the Server

#### For STDIO Mode:
```bash
cd string-processor-server
python string_processor_server.py
# Select: 1
```

#### For HTTP Mode:
```bash
cd string-processor-server
python string_processor_server.py
# Select: 2
```

### Step 3: Configure Client
1. Choose the appropriate configuration for your client
2. Update file paths to match your system
3. Save the configuration
4. Restart your MCP client

### Step 4: Verify Connection
1. Open your MCP client
2. Look for the "string-processor" server in available tools
3. Test with a simple command like: "Please reverse the string 'hello'"

## 🧪 Testing Connection

### STDIO Mode Testing
1. **Start Server**: Run server and select STDIO mode
2. **Check Logs**: Server should show "Starting String Processor MCP Server (STDIO mode)..."
3. **Client Test**: In your MCP client, try: "What tools are available?"
4. **Function Test**: Try: "Please reverse the string 'test'"

### HTTP Mode Testing
1. **Start Server**: Run server and select HTTP mode
2. **Browser Test**: Visit `http://localhost:8003` - should show server info
3. **Health Check**: Visit `http://localhost:8003/health`
4. **API Test**: 
   ```bash
   curl -X POST http://localhost:8003/mcp \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list", "params": {}}'
   ```

## 💡 Usage Examples

Once connected, you can use these commands in your MCP client:

### Basic String Operations
```
"Please reverse the string 'Hello World'"
"Convert 'hello world' to uppercase"
"Change 'HELLO WORLD' to title case"
```

### Session Management
```
"Show me my session statistics"
"What's my session ID?"
```

### Batch Processing
```
"Please reverse these strings: apple, banana, cherry"
"Convert these to uppercase: hello, world, mcp"
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Server Not Found
**Symptoms**: Client can't find the MCP server
**Solutions**:
- Verify file paths in configuration
- Ensure Python is in the correct path
- Check conda environment activation

#### 2. Import Errors
**Symptoms**: "ModuleNotFoundError: No module named 'mcp'"
**Solutions**:
```bash
# Activate environment
conda activate mcp-dev

# Verify installation
pip show mcp

# Reinstall if needed
pip install mcp
```

#### 3. HTTP Connection Issues
**Symptoms**: HTTP mode fails to start or connect
**Solutions**:
```bash
# Install dependencies
pip install fastapi uvicorn

# Check port availability
netstat -an | findstr 8003

# Test local connection
curl http://localhost:8003/health
```

#### 4. Permission Issues (Windows)
**Symptoms**: Access denied or execution policy errors
**Solutions**:
```powershell
# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Run as administrator if needed
```

### Debug Mode

Enable detailed logging by modifying the server:
```python
# In string_processor_server.py
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
```

### Environment Verification

Create a test script to verify your environment:
```python
# test_environment.py
import sys
import subprocess

print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")

try:
    import mcp
    print(f"MCP version: {mcp.__version__}")
except ImportError:
    print("MCP not installed")

try:
    import fastapi
    print(f"FastAPI version: {fastapi.__version__}")
except ImportError:
    print("FastAPI not installed")

# Test conda environment
result = subprocess.run(['conda', 'info', '--envs'], capture_output=True, text=True)
print("Conda environments:")
print(result.stdout)
```

## 📋 Configuration Templates

### Windows Template (STDIO)
```json
{
  "mcpServers": {
    "string-processor": {
      "command": "C:\\Users\\<USER>\\anaconda3\\envs\\mcp-dev\\python.exe",
      "args": [
        "C:\\Users\\<USER>\\Desktop\\杂项\\mcp\\string-processor-server\\string_processor_server.py"
      ],
      "env": {
        "CONDA_DEFAULT_ENV": "mcp-dev"
      }
    }
  }
}
```

### macOS/Linux Template (STDIO)
```json
{
  "mcpServers": {
    "string-processor": {
      "command": "/home/<USER>/anaconda3/envs/mcp-dev/bin/python",
      "args": [
        "/home/<USER>/mcp/string-processor-server/string_processor_server.py"
      ],
      "env": {
        "CONDA_DEFAULT_ENV": "mcp-dev"
      }
    }
  }
}
```

### HTTP Template (Any Platform)
```json
{
  "mcpServers": {
    "string-processor-http": {
      "url": "http://localhost:8003/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

## 🔐 Security Notes

### STDIO Mode
- Runs with user permissions
- No network exposure
- Secure for local development

### HTTP Mode
- Exposes server on network
- No authentication by default
- Use only on trusted networks
- Consider firewall configuration

## 📞 Support

If you encounter issues:

1. **Check Logs**: Server provides detailed logging
2. **Verify Environment**: Ensure mcp-dev is activated
3. **Test Manually**: Run server manually to see error messages
4. **Check Paths**: Verify all file paths in configuration
5. **Restart Client**: Always restart MCP client after configuration changes

For additional help, refer to:
- [MCP Official Documentation](https://modelcontextprotocol.io/)
- [Claude Desktop MCP Guide](https://docs.anthropic.com/claude/docs/mcp)
- Server README.md for detailed functionality
