# SSE Hello MCP Server

这是一个基于HTTP + Server-Sent Events (SSE) 的MCP服务器示例，展示了如何实现实时数据流和事件推送功能。

## 功能特性

### 🌐 HTTP + SSE 传输
- **传输协议**: HTTP + Server-Sent Events
- **实时数据流**: 支持持续的数据推送
- **Web兼容**: 可通过浏览器直接访问和测试
- **RESTful API**: 提供标准的HTTP端点

### 📊 实时数据流
- **模拟传感器数据**: 温度、湿度、CPU使用率等
- **服务器统计**: 运行时间、请求计数、活跃连接数
- **自动更新**: 每2秒推送新数据

### 🔧 MCP 工具
- **get_system_info** - 获取系统信息
  - 参数：`component` (string) - 要查询的组件类型
  - 支持：all, cpu, memory, network, disk

- **send_notification** - 发送通知消息
  - 参数：`message` (string) - 通知内容
  - 参数：`priority` (string) - 优先级 (low, normal, high, urgent)

- **calculate_metrics** - 计算模拟指标
  - 参数：`data_points` (number) - 要生成的数据点数量

### 📁 MCP 资源
- **sse://server-stats** - 服务器统计信息
- **sse://real-time-data** - 当前实时数据快照

## 技术实现

### 架构组件
- **FastAPI**: Web框架和HTTP服务器
- **FastMCP**: MCP协议实现
- **SSE-Starlette**: Server-Sent Events支持
- **Uvicorn**: ASGI服务器

### 依赖项
```bash
pip install fastapi uvicorn sse-starlette mcp
```

## 安装和运行

### 1. 激活MCP环境
```bash
# 从项目根目录运行
conda activate mcp-dev
```

### 2. 安装额外依赖
```bash
cd sse-hello-server
pip install fastapi uvicorn sse-starlette
```

### 3. 运行服务器
```bash
python sse_hello_server.py
```

或者使用uvicorn：
```bash
uvicorn sse_hello_server:app --reload --host 127.0.0.1 --port 8000
```

### 4. 验证服务器
服务器启动后，访问以下端点：

- **主页**: http://127.0.0.1:8000/
- **健康检查**: http://127.0.0.1:8000/health
- **实时数据流**: http://127.0.0.1:8000/stream
- **MCP端点**: http://127.0.0.1:8000/mcp

## 使用示例

### 浏览器测试SSE流
打开浏览器访问 http://127.0.0.1:8000/stream，你会看到实时数据流：

```
event: data_update
data: {"timestamp": "2025-01-03T10:30:00", "temperature": 25.3, "humidity": 65.2}

event: data_update
data: {"timestamp": "2025-01-03T10:30:02", "temperature": 25.1, "humidity": 65.8}
```

### MCP客户端配置
在MCP客户端（如Cursor）中添加以下配置：

```json
{
  "mcpServers": {
    "sse-hello-server": {
      "url": "http://127.0.0.1:8000/mcp",
      "env": {}
    }
  }
}
```

### 工具调用示例
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "get_system_info",
    "arguments": {
      "component": "cpu"
    }
  }
}
```

## 代码结构

### 主要组件
```python
# FastMCP实例
mcp = FastMCP("SSE-Hello-Server")

# FastAPI应用
app = FastAPI(title="SSE Hello MCP Server")

# 实时数据生成器
async def generate_real_time_data() -> AsyncGenerator[dict, None]:
    # 生成模拟数据流

# MCP工具和资源
@mcp.tool()
@mcp.resource()

# SSE端点
@app.get("/stream")
async def stream_data():
    return EventSourceResponse(event_generator())
```

### 数据流架构
```
Client Browser ←→ SSE Stream ←→ FastAPI ←→ Data Generator
     ↓
MCP Client ←→ HTTP/JSON ←→ FastMCP ←→ Tools & Resources
```

## 学习要点

1. **HTTP + SSE传输** - 了解如何在Web环境中使用MCP
2. **实时数据流** - 掌握Server-Sent Events的实现
3. **FastAPI集成** - 学习如何将MCP集成到Web应用
4. **异步编程** - 理解异步数据生成和处理
5. **Web兼容性** - 体验浏览器可访问的MCP服务器

## 扩展建议

1. **真实数据源**
   - 连接数据库
   - 集成外部API
   - 读取文件系统

2. **认证和安全**
   - API密钥验证
   - JWT令牌
   - CORS配置

3. **高级SSE功能**
   - 多频道支持
   - 客户端过滤
   - 断线重连

4. **监控和日志**
   - 性能指标
   - 错误追踪
   - 访问日志

## 故障排除

### 常见问题

1. **服务器无法启动**
   - 检查端口8000是否被占用
   - 确认所有依赖已安装

2. **SSE连接失败**
   - 检查防火墙设置
   - 验证浏览器支持SSE

3. **MCP工具不可用**
   - 确认MCP端点可访问
   - 检查客户端配置

### 调试技巧

1. 查看服务器日志：
```bash
uvicorn sse_hello_server:app --log-level debug
```

2. 测试SSE连接：
```bash
curl -N -H "Accept: text/event-stream" http://127.0.0.1:8000/stream
```

3. 测试MCP端点：
```bash
curl http://127.0.0.1:8000/mcp
```

## 性能考虑

- **连接限制**: 监控并发SSE连接数
- **内存使用**: 注意数据生成器的内存占用
- **网络带宽**: 控制数据推送频率
- **CPU使用**: 优化数据处理逻辑

## 相关资源

- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [Server-Sent Events 规范](https://html.spec.whatwg.org/multipage/server-sent-events.html)
- [MCP 官方文档](https://modelcontextprotocol.io/)
- [SSE-Starlette](https://github.com/sysid/sse-starlette)
