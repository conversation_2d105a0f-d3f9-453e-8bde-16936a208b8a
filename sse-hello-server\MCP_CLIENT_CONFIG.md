# MCP客户端配置指南

这个文档说明如何配置各种MCP客户端来连接到我们的String Processor MCP服务器。

## 🎯 服务器信息

- **服务器名称**: String Processor MCP Server
- **功能**: 字符串处理、会话管理
- **支持模式**: STDIO (本地) 和 HTTP (远程)

## 📋 可用工具

1. **reverse_string** - 反转字符串
2. **convert_case** - 大小写转换 (upper/lower/title)
3. **get_session_stats** - 获取会话统计
4. **process_batch** - 批量处理字符串

## 🔧 客户端配置

### 1. <PERSON>kt<PERSON> (本地STDIO)

在Claude Desktop的配置文件中添加：

**Windows路径**: `%APPDATA%\Claude\claude_desktop_config.json`
**macOS路径**: `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "string-processor": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Desktop\\杂项\\mcp\\sse-hello-server\\proper_mcp_server.py"
      ],
      "env": {
        "CONDA_DEFAULT_ENV": "mcp-dev"
      }
    }
  }
}
```

**注意**: 
- 替换路径为你的实际文件路径
- 确保已激活mcp-dev环境
- 服务器启动时选择模式1 (STDIO)

### 2. Cursor (本地STDIO)

在Cursor设置中添加MCP服务器：

```json
{
  "mcpServers": {
    "string-processor": {
      "command": "python",
      "args": [
        "C:\\Users\\<USER>\\Desktop\\杂项\\mcp\\sse-hello-server\\proper_mcp_server.py"
      ],
      "env": {
        "PATH": "C:\\Users\\<USER>\\anaconda3\\envs\\mcp-dev\\Scripts;C:\\Users\\<USER>\\anaconda3\\envs\\mcp-dev"
      }
    }
  }
}
```

### 3. HTTP模式 (远程访问)

对于支持HTTP MCP连接的客户端：

```json
{
  "mcpServers": {
    "string-processor-remote": {
      "url": "http://your-server-ip:8002/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

**获取服务器IP地址**:
```bash
# Windows
ipconfig | findstr IPv4

# Linux/macOS  
ip addr show | grep inet
```

## 🚀 启动步骤

### 本地STDIO模式

1. **激活环境**:
   ```bash
   conda activate mcp-dev
   ```

2. **启动服务器**:
   ```bash
   cd sse-hello-server
   python proper_mcp_server.py
   ```

3. **选择模式**: 输入 `1` 选择STDIO模式

4. **配置客户端**: 使用上面的STDIO配置

5. **重启客户端**: 重启Claude Desktop或Cursor

### 远程HTTP模式

1. **安装依赖**:
   ```bash
   conda activate mcp-dev
   pip install fastapi uvicorn
   ```

2. **启动服务器**:
   ```bash
   cd sse-hello-server
   python proper_mcp_server.py
   ```

3. **选择模式**: 输入 `2` 选择HTTP模式

4. **配置防火墙**: 确保端口8002开放

5. **配置客户端**: 使用HTTP配置，替换IP地址

## 🧪 测试连接

### STDIO模式测试

启动服务器后，你应该看到：
```
🌟 String Processor MCP Server
这是一个符合MCP协议标准的字符串处理服务器
支持字符串反转、大小写转换和会话管理功能
请选择运行模式:
1. STDIO模式 (本地MCP客户端连接)
2. HTTP模式 (支持远程访问)
```

选择1后，服务器会等待客户端连接。

### HTTP模式测试

选择2后，你会看到：
```
🌐 HTTP MCP服务器启动在: http://0.0.0.0:8002
🔗 远程访问地址: http://<your-ip>:8002
📡 MCP端点: http://<your-ip>:8002/mcp
```

可以通过浏览器访问 `http://localhost:8002` 测试服务器是否运行。

## 💡 使用示例

连接成功后，你可以在MCP客户端中使用以下功能：

### 字符串反转
```
请帮我反转字符串 "Hello World"
```

### 大小写转换
```
请将 "hello world" 转换为大写
```

### 批量处理
```
请将以下字符串都反转：["apple", "banana", "cherry"]
```

### 会话统计
```
显示我当前会话的统计信息
```

## 🔧 故障排除

### 常见问题

1. **"ModuleNotFoundError: No module named 'mcp'"**
   - 确保已激活mcp-dev环境
   - 运行 `conda activate mcp-dev`

2. **客户端无法连接**
   - 检查文件路径是否正确
   - 确保Python可执行文件路径正确
   - 重启MCP客户端

3. **HTTP模式启动失败**
   - 安装依赖: `pip install fastapi uvicorn`
   - 检查端口8002是否被占用

4. **远程连接失败**
   - 检查防火墙设置
   - 确认服务器IP地址正确
   - 验证端口8002是否开放

### 调试技巧

1. **查看服务器日志**:
   服务器会输出详细的日志信息，包括连接状态和错误信息。

2. **测试基本连接**:
   ```bash
   # 测试HTTP端点
   curl http://localhost:8002/
   ```

3. **验证MCP协议**:
   ```bash
   # 测试MCP端点
   curl -X POST http://localhost:8002/mcp \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {}}'
   ```

## 📚 更多资源

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [Claude Desktop MCP配置](https://docs.anthropic.com/claude/docs/mcp)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
