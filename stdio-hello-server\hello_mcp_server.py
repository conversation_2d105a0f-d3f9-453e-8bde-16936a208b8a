#!/usr/bin/env python3
"""
一个简单的MCP Server示例
这个服务器提供基本的工具和资源功能
"""

import asyncio
import logging
from typing import Any, Sequence

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("hello-mcp-server")

# 创建服务器实例
server = Server("hello-mcp-server")

@server.list_resources()
async def handle_list_resources() -> list[Resource]:
    """
    列出可用的资源
    """
    return [
        Resource(
            uri="hello://greeting",
            name="Hello Greeting",
            description="A simple greeting message",
            mimeType="text/plain",
        ),
        Resource(
            uri="hello://info",
            name="Server Info",
            description="Information about this MCP server",
            mimeType="text/plain",
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """
    读取指定的资源
    """
    if uri == "hello://greeting":
        return "Hello from MCP Server! 你好，欢迎使用MCP服务器！"
    elif uri == "hello://info":
        return """
MCP Server Information:
- Name: Hello MCP Server
- Version: 1.0.0
- Description: A simple example MCP server for learning
- Author: Your Name
- Created with: Python MCP SDK
        """
    else:
        raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """
    列出可用的工具
    """
    return [
        Tool(
            name="say_hello",
            description="Say hello to someone",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "The name of the person to greet"
                    }
                },
                "required": ["name"]
            }
        ),
        Tool(
            name="add_numbers",
            description="Add two numbers together",
            inputSchema={
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "First number"
                    },
                    "b": {
                        "type": "number", 
                        "description": "Second number"
                    }
                },
                "required": ["a", "b"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any]) -> list[TextContent]:
    """
    执行工具调用
    """
    if name == "say_hello":
        person_name = arguments.get("name", "World")
        greeting = f"Hello, {person_name}! 你好，{person_name}！"
        return [TextContent(type="text", text=greeting)]
    
    elif name == "add_numbers":
        a = arguments.get("a", 0)
        b = arguments.get("b", 0)
        result = a + b
        return [TextContent(
            type="text", 
            text=f"The sum of {a} and {b} is {result}"
        )]
    
    else:
        raise ValueError(f"Unknown tool: {name}")

async def main():
    """
    主函数 - 启动MCP服务器
    """
    # 使用stdio传输运行服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="hello-mcp-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
