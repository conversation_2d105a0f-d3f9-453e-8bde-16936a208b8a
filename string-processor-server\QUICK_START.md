# Quick Start Guide - String Processor MCP Server

This guide will get you up and running with the String Processor MCP Server in under 5 minutes.

## 🚀 Quick Setup

### 1. Environment Preparation
```bash
# Activate the MCP development environment
conda activate mcp-dev

# Install HTTP mode dependencies (recommended)
pip install fastapi uvicorn
```

### 2. Start the Server
```bash
# Navigate to the server directory
cd string-processor-server

# Run the server
python string_processor_server.py

# Select mode when prompted:
# 1 = STDIO mode (for local MCP clients like Claude <PERSON>)
# 2 = HTTP mode (for remote access and testing) ← RECOMMENDED
```

### 3. Test HTTP Mode (Recommended)
If you selected HTTP mode (option 2), you can immediately test:

**Browser Test:**
- Open: http://localhost:8003
- You should see server information

**Health Check:**
- Open: http://localhost:8003/health
- Should show: `{"status":"healthy",...}`

## 🎯 Quick Demo

### Test String Reversal
Once your server is running in HTTP mode, you can test the MCP functionality:

1. **Server Info**: Visit http://localhost:8003
2. **API Docs**: Visit http://localhost:8003/docs
3. **Test Tool**: Use the interactive API docs to test the `reverse_string` tool

### Example MCP Request
```json
POST http://localhost:8003/mcp
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "reverse_string",
    "arguments": {
      "text": "Hello MCP!",
      "session_id": "demo-user"
    }
  }
}
```

**Expected Response:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [{
      "type": "text",
      "text": "{\n  \"operation\": \"reverse_string\",\n  \"session_id\": \"demo-user\",\n  \"input\": \"Hello MCP!\",\n  \"output\": \"!PCM olleH\",\n  \"timestamp\": \"2025-01-03T...\"\n}"
    }]
  }
}
```

## 🔧 MCP Client Setup

### For Claude Desktop
Add to your configuration file:

**Windows:** `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "string-processor": {
      "url": "http://localhost:8003/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

**Note:** HTTP mode is recommended over STDIO due to current compatibility issues.

## 🎮 Available Features

### Core Tools
- **reverse_string** - Reverse any text
- **convert_case** - Convert to upper/lower/title case
- **process_batch** - Process multiple strings at once
- **get_session_stats** - View session statistics

### Session Management
- Each user gets an isolated session
- Operation history is tracked
- Statistics are maintained per session

### Example Usage in MCP Client
```
User: "Please reverse the string 'Hello World'"
Server: "dlroW olleH"

User: "Convert 'hello world' to uppercase"  
Server: "HELLO WORLD"

User: "Show my session statistics"
Server: [Displays session info with operation counts]
```

## 🐛 Troubleshooting

### Server Won't Start
```bash
# Check environment
conda activate mcp-dev
python -c "import mcp; print('MCP OK')"

# Check dependencies for HTTP mode
pip install fastapi uvicorn
```

### Port Already in Use
```bash
# Check what's using port 8003
netstat -an | findstr 8003

# Kill the process or change port in server code
```

### STDIO Mode Issues
- **Known Issue**: Current STDIO implementation has compatibility problems
- **Solution**: Use HTTP mode instead (works perfectly)
- HTTP mode provides all the same functionality with additional benefits

## 📚 Next Steps

1. **Read Full Documentation**: See `README.md` for complete feature list
2. **Client Configuration**: See `CLIENT_CONFIG.md` for detailed setup
3. **Extend Functionality**: Modify the server to add your own tools
4. **Production Deployment**: Configure for your specific environment

## 💡 Pro Tips

1. **Use HTTP Mode**: More reliable and easier to debug
2. **Monitor Sessions**: Check http://localhost:8003 for active session count
3. **Test with API Docs**: Use http://localhost:8003/docs for interactive testing
4. **Session IDs**: Provide consistent session_id for user tracking
5. **Batch Processing**: Use `process_batch` for multiple operations

## 🔗 Useful URLs (HTTP Mode)

- **Server Info**: http://localhost:8003
- **Health Check**: http://localhost:8003/health  
- **API Documentation**: http://localhost:8003/docs
- **MCP Endpoint**: http://localhost:8003/mcp

---

**Ready to start?** Run `python string_processor_server.py` and select option 2! 🚀
