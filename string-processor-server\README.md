# String Processor MCP Server

A comprehensive Model Context Protocol (MCP) server for string processing with advanced session management capabilities. This server demonstrates interactive MCP functionality with user session isolation and continuous request handling.

## 🌟 Features

### Core Capabilities
- **String Reversal** - Reverse any text string
- **Case Conversion** - Convert text to uppercase, lowercase, or title case
- **Batch Processing** - Process multiple strings with the same operation
- **Session Management** - Track user sessions with isolated state
- **Operation History** - Maintain history of all operations per session
- **Dual Transport** - Support both STDIO and HTTP transport modes

### Advanced Features
- **User Session Isolation** - Each user gets their own session with independent state
- **Continuous Request Handling** - Process multiple requests within the same session
- **Statistics Tracking** - Track operation counts and session metrics
- **Remote Access** - HTTP mode supports remote client connections
- **Real-time Monitoring** - View active sessions and server statistics

## 🏗️ Architecture

### Transport Modes

| Mode | Use Case | Connection Type | Best For |
|------|----------|----------------|----------|
| **STDIO** | Local clients | Process pipes | <PERSON>, <PERSON>urs<PERSON> |
| **HTTP** | Remote access | HTTP/JSON-RPC | Web clients, remote tools |

### Session Management
```
User Request → Session Manager → String Processor → Response
     ↓              ↓                    ↓             ↓
Session ID → Update Activity → Log Operation → Track Stats
```

## 📋 Available Tools

### 1. reverse_string
Reverse any text string with session tracking.

**Parameters:**
- `text` (string, required) - The text to reverse
- `session_id` (string, optional) - Session ID for tracking

**Example:**
```json
{
  "text": "Hello World",
  "session_id": "user123"
}
```

**Response:**
```json
{
  "operation": "reverse_string",
  "session_id": "user123", 
  "input": "Hello World",
  "output": "dlroW olleH",
  "timestamp": "2025-01-03T10:30:00"
}
```

### 2. convert_case
Convert string case with session tracking.

**Parameters:**
- `text` (string, required) - The text to convert
- `case` (string, required) - Target case: "upper", "lower", or "title"
- `session_id` (string, optional) - Session ID for tracking

**Example:**
```json
{
  "text": "hello world",
  "case": "upper",
  "session_id": "user123"
}
```

### 3. get_session_stats
Get comprehensive statistics for a user session.

**Parameters:**
- `session_id` (string, required) - Session ID to get stats for

**Response includes:**
- Session creation time and last activity
- Operation counts by type
- Recent operation history
- Total request count

### 4. process_batch
Process multiple strings with the same operation.

**Parameters:**
- `texts` (array, required) - Array of strings to process
- `operation` (string, required) - Operation: "reverse", "upper", "lower", "title"
- `session_id` (string, optional) - Session ID for tracking

## 📁 Available Resources

### mcp://sessions/list
List all active user sessions with basic information.

### mcp://sessions/{session_id}
Get detailed information about a specific session including:
- Full operation history
- User preferences
- Detailed statistics

### mcp://server/info
Get comprehensive server information including:
- Server capabilities
- Supported transport modes
- Version information

## 🚀 Installation & Setup

### Prerequisites
- Python 3.11+
- Conda environment `mcp-dev`
- MCP Python SDK

### 1. Environment Setup
```bash
# Activate the MCP development environment
conda activate mcp-dev

# Install additional dependencies for HTTP mode (optional)
pip install fastapi uvicorn
```

### 2. Running the Server

#### STDIO Mode (Local Clients)
```bash
cd string-processor-server
python string_processor_server.py
# Select: 1 (STDIO mode)
```

#### HTTP Mode (Remote Access)
```bash
cd string-processor-server  
python string_processor_server.py
# Select: 2 (HTTP mode)
```

The HTTP server will start on `http://0.0.0.0:8003`

## 🔧 Client Configuration

### Claude Desktop (STDIO)
Add to your Claude Desktop configuration:

**Windows:** `%APPDATA%\Claude\claude_desktop_config.json`
**macOS:** `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "string-processor": {
      "command": "python",
      "args": [
        "C:\\path\\to\\mcp\\string-processor-server\\string_processor_server.py"
      ],
      "env": {
        "CONDA_DEFAULT_ENV": "mcp-dev"
      }
    }
  }
}
```

### Cursor (STDIO)
```json
{
  "mcpServers": {
    "string-processor": {
      "command": "python",
      "args": [
        "C:\\path\\to\\mcp\\string-processor-server\\string_processor_server.py"
      ],
      "env": {
        "PATH": "C:\\path\\to\\anaconda3\\envs\\mcp-dev\\Scripts"
      }
    }
  }
}
```

### HTTP Mode (Remote Clients)
```json
{
  "mcpServers": {
    "string-processor-remote": {
      "url": "http://your-server-ip:8003/mcp",
      "headers": {
        "Content-Type": "application/json"
      }
    }
  }
}
```

## 💡 Usage Examples

### Basic String Operations
```
User: "Please reverse the string 'Hello MCP'"
Server: Processes with reverse_string tool
Result: "PCM olleH"
```

### Session-Aware Processing
```
User: "Convert 'hello world' to uppercase"
Server: Creates/uses session, tracks operation
Result: "HELLO WORLD" + session statistics updated
```

### Batch Processing
```
User: "Please reverse these strings: ['apple', 'banana', 'cherry']"
Server: Uses process_batch tool
Result: ['elppa', 'ananab', 'yrrehc']
```

### Session Management
```
User: "Show me my session statistics"
Server: Uses get_session_stats tool
Result: Complete session history and metrics
```

## 🧪 Testing

### STDIO Mode Testing
1. Start server in STDIO mode
2. Configure MCP client (Claude Desktop/Cursor)
3. Restart client application
4. Test string operations through client interface

### HTTP Mode Testing
1. Start server in HTTP mode
2. Test basic connectivity:
   ```bash
   curl http://localhost:8003/health
   ```
3. Test MCP endpoint:
   ```bash
   curl -X POST http://localhost:8003/mcp \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
   ```

## 🔍 Troubleshooting

### Common Issues

1. **"ModuleNotFoundError: No module named 'mcp'"**
   - Ensure mcp-dev environment is activated
   - Run: `conda activate mcp-dev`

2. **"HTTP mode requires additional dependencies"**
   - Install FastAPI: `pip install fastapi uvicorn`

3. **STDIO mode TaskGroup error (Known Issue)**
   - Current STDIO implementation has compatibility issues
   - **Workaround**: Use HTTP mode which works perfectly
   - HTTP mode provides the same MCP functionality with remote access benefits

4. **Client connection fails**
   - Verify file paths in client configuration
   - Check Python executable path
   - Restart MCP client application

5. **Port 8003 already in use**
   - Change port in server code or kill existing process
   - Check: `netstat -an | findstr 8003`

### Debug Mode
Enable detailed logging by modifying the logging level:
```python
logging.basicConfig(level=logging.DEBUG)
```

### Session Debugging
Monitor session activity through the server logs or by calling:
- `mcp://sessions/list` resource
- `get_session_stats` tool

## 🔐 Security Considerations

### STDIO Mode
- Runs locally, inherits user permissions
- No network exposure
- Secure for local development

### HTTP Mode
- Exposes server on network (0.0.0.0:8003)
- No authentication implemented
- Suitable for trusted networks only
- Consider firewall rules for production

## 📈 Performance

### Session Management
- In-memory session storage
- Automatic session cleanup (can be implemented)
- Efficient operation tracking

### Scalability
- Single-threaded for STDIO mode
- Async HTTP handling for concurrent requests
- Memory usage scales with active sessions

## 🛠️ Development

### Extending Functionality
1. Add new tools in `handle_list_tools()` and `handle_call_tool()`
2. Add new resources in `handle_list_resources()` and `handle_read_resource()`
3. Extend session management for additional user data

### Custom Operations
Example of adding a new string operation:
```python
# In handle_list_tools(), add:
Tool(
    name="count_words",
    description="Count words in text",
    inputSchema={
        "type": "object",
        "properties": {
            "text": {"type": "string", "description": "Text to analyze"}
        },
        "required": ["text"]
    }
)

# In handle_call_tool(), add:
elif name == "count_words":
    text = arguments.get("text", "")
    word_count = len(text.split())
    return [TextContent(type="text", text=f"Word count: {word_count}")]
```

## 📚 Related Resources

- [MCP Official Documentation](https://modelcontextprotocol.io/)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [Claude Desktop MCP Setup](https://docs.anthropic.com/claude/docs/mcp)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
