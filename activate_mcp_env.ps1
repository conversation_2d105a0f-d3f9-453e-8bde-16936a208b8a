# MCP Development Environment Activation Script
Write-Host "Activating MCP development environment..." -ForegroundColor Green

# Activate conda environment
conda activate mcp-dev

Write-Host ""
Write-Host "MCP environment activated!" -ForegroundColor Green
Write-Host ""

# Show Python version
Write-Host "Python version:" -ForegroundColor Yellow
python --version

Write-Host ""

# Show MCP version
Write-Host "Installed MCP version:" -ForegroundColor Yellow
pip show mcp | Select-String "Version"

Write-Host ""
Write-Host "You are now ready to develop MCP servers!" -ForegroundColor Cyan
Write-Host "Type 'exit' to leave this environment." -ForegroundColor Gray
Write-Host ""
