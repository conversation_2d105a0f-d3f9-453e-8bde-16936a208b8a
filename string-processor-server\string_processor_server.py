#!/usr/bin/env python3
"""
String Processor MCP Server
A comprehensive MCP server for string processing with session management
Supports both STDIO and HTTP transport modes
"""

import asyncio
import logging
import json
import uuid
import sys
from datetime import datetime
from typing import Any, Dict, List

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    LoggingLevel
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("string-processor-mcp")

# Create MCP server instance
server = Server("string-processor-mcp")

# Session Management
class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, Dict[str, Any]] = {}
    
    def create_session(self, user_id: str = None) -> str:
        """Create a new session"""
        session_id = user_id or str(uuid.uuid4())[:8]
        self.sessions[session_id] = {
            "created_at": datetime.now(),
            "last_activity": datetime.now(),
            "message_history": [],
            "user_preferences": {},
            "statistics": {
                "total_requests": 0,
                "reverse_count": 0,
                "uppercase_count": 0,
                "lowercase_count": 0,
                "title_count": 0
            }
        }
        logger.info(f"Created session: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Dict[str, Any]:
        """Get session information"""
        if session_id not in self.sessions:
            self.create_session(session_id)
        return self.sessions[session_id]
    
    def update_activity(self, session_id: str):
        """Update session activity"""
        if session_id in self.sessions:
            self.sessions[session_id]["last_activity"] = datetime.now()
    
    def add_to_history(self, session_id: str, operation: str, input_text: str, output_text: str):
        """Add operation to history"""
        session = self.get_session(session_id)
        session["message_history"].append({
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "input": input_text,
            "output": output_text
        })
        session["statistics"]["total_requests"] += 1
        if operation in session["statistics"]:
            session["statistics"][f"{operation}_count"] += 1

# Global session manager
session_manager = SessionManager()

# MCP Resources
@server.list_resources()
async def handle_list_resources() -> List[Resource]:
    """List available resources"""
    return [
        Resource(
            uri="mcp://sessions/list",
            name="Active Sessions",
            description="List of all active user sessions",
            mimeType="application/json",
        ),
        Resource(
            uri="mcp://sessions/{session_id}",
            name="Session Details", 
            description="Detailed information about a specific session",
            mimeType="application/json",
        ),
        Resource(
            uri="mcp://server/info",
            name="Server Information",
            description="Information about this MCP server",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Read specified resource"""
    if uri == "mcp://sessions/list":
        sessions_info = {}
        for session_id, session_data in session_manager.sessions.items():
            sessions_info[session_id] = {
                "created_at": session_data["created_at"].isoformat(),
                "last_activity": session_data["last_activity"].isoformat(),
                "total_requests": session_data["statistics"]["total_requests"]
            }
        return json.dumps({
            "active_sessions": len(session_manager.sessions),
            "sessions": sessions_info
        }, indent=2, ensure_ascii=False)
    
    elif uri.startswith("mcp://sessions/"):
        session_id = uri.split("/")[-1]
        session = session_manager.get_session(session_id)
        session_copy = session.copy()
        session_copy["created_at"] = session["created_at"].isoformat()
        session_copy["last_activity"] = session["last_activity"].isoformat()
        return json.dumps(session_copy, indent=2, ensure_ascii=False)
    
    elif uri == "mcp://server/info":
        return json.dumps({
            "name": "String Processor MCP Server",
            "version": "1.0.0",
            "description": "A comprehensive MCP server for string processing with session management",
            "capabilities": [
                "String reversal",
                "Case conversion (upper/lower/title)",
                "Session management",
                "Operation history",
                "Batch processing"
            ],
            "transport_modes": ["STDIO", "HTTP"],
            "author": "MCP Learning Project",
            "created_at": datetime.now().isoformat()
        }, indent=2, ensure_ascii=False)
    
    else:
        raise ValueError(f"Unknown resource: {uri}")

# MCP Tools
@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools"""
    return [
        Tool(
            name="reverse_string",
            description="Reverse a string with session tracking",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "The text to reverse"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Optional session ID for tracking (auto-generated if not provided)"
                    }
                },
                "required": ["text"]
            }
        ),
        Tool(
            name="convert_case",
            description="Convert string case with session tracking",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "The text to convert"
                    },
                    "case": {
                        "type": "string",
                        "enum": ["upper", "lower", "title"],
                        "description": "Target case: upper, lower, or title"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Optional session ID for tracking"
                    }
                },
                "required": ["text", "case"]
            }
        ),
        Tool(
            name="get_session_stats",
            description="Get statistics for a user session",
            inputSchema={
                "type": "object",
                "properties": {
                    "session_id": {
                        "type": "string",
                        "description": "Session ID to get stats for"
                    }
                },
                "required": ["session_id"]
            }
        ),
        Tool(
            name="process_batch",
            description="Process multiple strings with the same operation",
            inputSchema={
                "type": "object",
                "properties": {
                    "texts": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Array of texts to process"
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["reverse", "upper", "lower", "title"],
                        "description": "Operation to apply to all texts"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Optional session ID for tracking"
                    }
                },
                "required": ["texts", "operation"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Execute tool calls"""
    
    if name == "reverse_string":
        text = arguments.get("text", "")
        session_id = arguments.get("session_id") or session_manager.create_session()
        
        if not text:
            return [TextContent(
                type="text",
                text="Error: Input text cannot be empty"
            )]
        
        reversed_text = text[::-1]
        session_manager.update_activity(session_id)
        session_manager.add_to_history(session_id, "reverse", text, reversed_text)
        
        result = {
            "operation": "reverse_string",
            "session_id": session_id,
            "input": text,
            "output": reversed_text,
            "timestamp": datetime.now().isoformat()
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(result, ensure_ascii=False, indent=2)
        )]
    
    elif name == "convert_case":
        text = arguments.get("text", "")
        case = arguments.get("case", "upper")
        session_id = arguments.get("session_id") or session_manager.create_session()
        
        if not text:
            return [TextContent(
                type="text",
                text="Error: Input text cannot be empty"
            )]
        
        if case == "upper":
            converted_text = text.upper()
        elif case == "lower":
            converted_text = text.lower()
        elif case == "title":
            converted_text = text.title()
        else:
            return [TextContent(
                type="text",
                text=f"Error: Unsupported case type '{case}'"
            )]
        
        session_manager.update_activity(session_id)
        session_manager.add_to_history(session_id, case, text, converted_text)
        
        result = {
            "operation": f"convert_case_{case}",
            "session_id": session_id,
            "input": text,
            "output": converted_text,
            "timestamp": datetime.now().isoformat()
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(result, ensure_ascii=False, indent=2)
        )]
    
    elif name == "get_session_stats":
        session_id = arguments.get("session_id")
        
        if not session_id:
            return [TextContent(
                type="text",
                text="Error: session_id is required"
            )]
        
        session = session_manager.get_session(session_id)
        
        stats = {
            "session_id": session_id,
            "created_at": session["created_at"].isoformat(),
            "last_activity": session["last_activity"].isoformat(),
            "statistics": session["statistics"],
            "history_count": len(session["message_history"]),
            "recent_operations": session["message_history"][-5:] if session["message_history"] else []
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(stats, ensure_ascii=False, indent=2)
        )]
    
    elif name == "process_batch":
        texts = arguments.get("texts", [])
        operation = arguments.get("operation", "reverse")
        session_id = arguments.get("session_id") or session_manager.create_session()
        
        if not texts:
            return [TextContent(
                type="text",
                text="Error: texts array is required"
            )]
        
        results = []
        for text in texts:
            if operation == "reverse":
                processed = text[::-1]
            elif operation == "upper":
                processed = text.upper()
            elif operation == "lower":
                processed = text.lower()
            elif operation == "title":
                processed = text.title()
            else:
                processed = f"Error: Unsupported operation '{operation}'"
            
            results.append({
                "input": text,
                "output": processed
            })
            
            session_manager.add_to_history(session_id, operation, text, processed)
        
        session_manager.update_activity(session_id)
        
        batch_result = {
            "operation": f"batch_{operation}",
            "session_id": session_id,
            "processed_count": len(texts),
            "results": results,
            "timestamp": datetime.now().isoformat()
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(batch_result, ensure_ascii=False, indent=2)
        )]
    
    else:
        return [TextContent(
            type="text",
            text=f"Error: Unknown tool '{name}'"
        )]

# Main functions
async def main_stdio():
    """Run server in STDIO mode for local MCP client connections"""
    logger.info("Starting String Processor MCP Server (STDIO mode)...")

    try:
        # Use a more compatible approach for STDIO server
        from mcp.server.stdio import stdio_server

        async with stdio_server() as streams:
            read_stream, write_stream = streams

            initialization_options = InitializationOptions(
                server_name="string-processor-mcp",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            )

            await server.run(
                read_stream,
                write_stream,
                initialization_options,
            )

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"STDIO server error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise

async def main_http():
    """Run server in HTTP mode for remote access"""
    try:
        import uvicorn
        from fastapi import FastAPI, HTTPException
        from fastapi.middleware.cors import CORSMiddleware

        logger.info("Starting String Processor MCP Server (HTTP mode)...")

        app = FastAPI(
            title="String Processor MCP Server",
            description="A comprehensive MCP server for string processing with session management",
            version="1.0.0"
        )

        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        @app.get("/")
        async def root():
            """Server information endpoint"""
            return {
                "name": "String Processor MCP Server",
                "version": "1.0.0",
                "description": "A comprehensive MCP server for string processing with session management",
                "mode": "HTTP",
                "endpoints": {
                    "mcp": "/mcp",
                    "health": "/health",
                    "docs": "/docs"
                },
                "capabilities": [
                    "String reversal",
                    "Case conversion",
                    "Session management",
                    "Batch processing",
                    "Remote access"
                ],
                "active_sessions": len(session_manager.sessions),
                "timestamp": datetime.now().isoformat()
            }

        @app.get("/health")
        async def health():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "active_sessions": len(session_manager.sessions),
                "timestamp": datetime.now().isoformat()
            }

        @app.post("/mcp")
        async def mcp_endpoint(request: dict):
            """MCP protocol endpoint"""
            try:
                method = request.get("method")
                params = request.get("params", {})
                request_id = request.get("id")

                if method == "initialize":
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {
                                "tools": {},
                                "resources": {}
                            },
                            "serverInfo": {
                                "name": "string-processor-mcp",
                                "version": "1.0.0"
                            }
                        }
                    }

                elif method == "tools/list":
                    tools = await handle_list_tools()
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "tools": [tool.model_dump() for tool in tools]
                        }
                    }

                elif method == "tools/call":
                    name = params.get("name")
                    arguments = params.get("arguments", {})
                    result = await handle_call_tool(name, arguments)
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "content": [content.model_dump() for content in result]
                        }
                    }

                elif method == "resources/list":
                    resources = await handle_list_resources()
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "resources": [resource.model_dump() for resource in resources]
                        }
                    }

                elif method == "resources/read":
                    uri = params.get("uri")
                    content = await handle_read_resource(uri)
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "contents": [{
                                "uri": uri,
                                "mimeType": "application/json",
                                "text": content
                            }]
                        }
                    }

                else:
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "error": {
                            "code": -32601,
                            "message": f"Method not found: {method}"
                        }
                    }

            except Exception as e:
                logger.error(f"MCP request processing error: {e}")
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32603,
                        "message": f"Internal error: {str(e)}"
                    }
                }

        config = uvicorn.Config(
            app,
            host="0.0.0.0",
            port=8003,
            log_level="info"
        )
        server_instance = uvicorn.Server(config)

        logger.info("🌐 HTTP MCP Server started at: http://0.0.0.0:8003")
        logger.info("🔗 Remote access URL: http://<your-ip>:8003")
        logger.info("📡 MCP endpoint: http://<your-ip>:8003/mcp")
        logger.info("📖 API docs: http://<your-ip>:8003/docs")

        await server_instance.serve()

    except ImportError as e:
        logger.error(f"HTTP mode requires additional dependencies: pip install fastapi uvicorn")
        logger.error(f"Missing module: {e}")
        logger.info("Falling back to STDIO mode...")
        await main_stdio()
    except Exception as e:
        logger.error(f"HTTP server startup failed: {e}")
        logger.info("Falling back to STDIO mode...")
        await main_stdio()

def show_menu():
    """Display startup menu"""
    print("=" * 60)
    print("🌟 String Processor MCP Server")
    print("=" * 60)
    print("A comprehensive MCP server for string processing with session management")
    print("Supports string reversal, case conversion, and batch processing")
    print("=" * 60)
    print()
    print("Please select running mode:")
    print("1. STDIO mode (for local MCP client connections)")
    print("2. HTTP mode (supports remote access)")
    print("3. Exit")
    print()

async def main():
    """Main function"""
    show_menu()

    try:
        choice = input("Enter your choice (1-3): ").strip()

        if choice == "1":
            await main_stdio()
        elif choice == "2":
            await main_http()
        elif choice == "3":
            print("Goodbye!")
            return
        else:
            print("Invalid choice, defaulting to STDIO mode...")
            await main_stdio()

    except KeyboardInterrupt:
        print("\nServer stopped")
    except Exception as e:
        logger.error(f"Startup failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
