#!/usr/bin/env python3
"""
基于Server-Sent Events (SSE) 的MCP服务器示例
这个服务器展示了如何使用HTTP+SSE传输方式实现MCP协议
包含实时数据流和事件推送功能
"""

import asyncio
import json
import logging
import time
import random
from datetime import datetime
from typing import Any, AsyncGenerator
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from sse_starlette import EventSourceResponse

from mcp.server.fastmcp import FastMCP
from mcp.types import TextContent

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("sse-hello-mcp-server")

# 创建FastMCP实例
mcp = FastMCP("SSE-Hello-Server")

# 全局状态存储
server_stats = {
    "start_time": datetime.now(),
    "requests_count": 0,
    "active_connections": 0,
    "last_activity": datetime.now()
}

# 模拟实时数据源
async def generate_real_time_data() -> AsyncGenerator[dict, None]:
    """生成模拟的实时数据流"""
    while True:
        data = {
            "timestamp": datetime.now().isoformat(),
            "temperature": round(random.uniform(20.0, 30.0), 2),
            "humidity": round(random.uniform(40.0, 80.0), 2),
            "cpu_usage": round(random.uniform(10.0, 90.0), 2),
            "memory_usage": round(random.uniform(30.0, 85.0), 2),
            "random_value": random.randint(1, 100)
        }
        yield data
        await asyncio.sleep(2)  # 每2秒生成一次数据

# MCP 资源定义
@mcp.resource("sse://server-stats")
def get_server_stats() -> str:
    """获取服务器统计信息"""
    uptime = datetime.now() - server_stats["start_time"]
    stats = {
        "server_name": "SSE Hello MCP Server",
        "version": "1.0.0",
        "uptime_seconds": int(uptime.total_seconds()),
        "uptime_formatted": str(uptime).split('.')[0],
        "requests_count": server_stats["requests_count"],
        "active_connections": server_stats["active_connections"],
        "last_activity": server_stats["last_activity"].isoformat(),
        "transport_type": "HTTP + Server-Sent Events"
    }
    return json.dumps(stats, indent=2, ensure_ascii=False)

@mcp.resource("sse://real-time-data")
def get_current_data() -> str:
    """获取当前实时数据快照"""
    data = {
        "timestamp": datetime.now().isoformat(),
        "temperature": round(random.uniform(20.0, 30.0), 2),
        "humidity": round(random.uniform(40.0, 80.0), 2),
        "cpu_usage": round(random.uniform(10.0, 90.0), 2),
        "memory_usage": round(random.uniform(30.0, 85.0), 2),
        "status": "active"
    }
    return json.dumps(data, indent=2, ensure_ascii=False)

# MCP 工具定义
@mcp.tool()
def get_system_info(component: str = "all") -> list[TextContent]:
    """
    获取系统信息
    
    Args:
        component: 要查询的组件 (all, cpu, memory, network, disk)
    """
    server_stats["requests_count"] += 1
    server_stats["last_activity"] = datetime.now()
    
    if component == "all":
        info = {
            "system": "SSE MCP Server",
            "cpu_cores": 4,
            "memory_total": "8GB",
            "disk_space": "256GB SSD",
            "network": "Ethernet 1Gbps",
            "os": "Python Runtime",
            "mcp_version": "1.10.1"
        }
    elif component == "cpu":
        info = {
            "cpu_usage": f"{random.uniform(10, 90):.1f}%",
            "cpu_cores": 4,
            "cpu_frequency": "2.4GHz"
        }
    elif component == "memory":
        info = {
            "memory_usage": f"{random.uniform(30, 85):.1f}%",
            "memory_total": "8GB",
            "memory_available": f"{random.uniform(1, 5):.1f}GB"
        }
    else:
        info = {"error": f"Unknown component: {component}"}
    
    return [TextContent(
        type="text",
        text=json.dumps(info, indent=2, ensure_ascii=False)
    )]

@mcp.tool()
def send_notification(message: str, priority: str = "normal") -> list[TextContent]:
    """
    发送通知消息
    
    Args:
        message: 通知内容
        priority: 优先级 (low, normal, high, urgent)
    """
    server_stats["requests_count"] += 1
    server_stats["last_activity"] = datetime.now()
    
    notification = {
        "id": f"notif_{int(time.time())}",
        "message": message,
        "priority": priority,
        "timestamp": datetime.now().isoformat(),
        "status": "sent"
    }
    
    return [TextContent(
        type="text",
        text=f"通知已发送: {json.dumps(notification, indent=2, ensure_ascii=False)}"
    )]

@mcp.tool()
def calculate_metrics(data_points: int = 10) -> list[TextContent]:
    """
    计算模拟指标
    
    Args:
        data_points: 要生成的数据点数量
    """
    server_stats["requests_count"] += 1
    server_stats["last_activity"] = datetime.now()
    
    # 生成模拟数据
    values = [random.uniform(0, 100) for _ in range(data_points)]
    
    metrics = {
        "data_points": data_points,
        "values": [round(v, 2) for v in values],
        "statistics": {
            "mean": round(sum(values) / len(values), 2),
            "min": round(min(values), 2),
            "max": round(max(values), 2),
            "range": round(max(values) - min(values), 2)
        },
        "generated_at": datetime.now().isoformat()
    }
    
    return [TextContent(
        type="text",
        text=json.dumps(metrics, indent=2, ensure_ascii=False)
    )]

# FastAPI 应用
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("SSE MCP Server starting...")
    yield
    logger.info("SSE MCP Server shutting down...")

app = FastAPI(
    title="SSE Hello MCP Server",
    description="基于Server-Sent Events的MCP服务器示例",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/")
async def root():
    """根路径 - 服务器信息"""
    return {
        "name": "SSE Hello MCP Server",
        "version": "1.0.0",
        "transport": "HTTP + Server-Sent Events",
        "endpoints": {
            "mcp": "/mcp",
            "sse_stream": "/stream",
            "health": "/health"
        },
        "uptime": str(datetime.now() - server_stats["start_time"]).split('.')[0]
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "uptime": str(datetime.now() - server_stats["start_time"]).split('.')[0]
    }

@app.get("/stream")
async def stream_data():
    """SSE数据流端点"""
    server_stats["active_connections"] += 1
    
    async def event_generator():
        try:
            async for data in generate_real_time_data():
                yield {
                    "event": "data_update",
                    "data": json.dumps(data)
                }
        except asyncio.CancelledError:
            logger.info("SSE connection cancelled")
        finally:
            server_stats["active_connections"] -= 1
    
    return EventSourceResponse(event_generator())

# 挂载MCP服务器
app.mount("/mcp", mcp.create_app())

if __name__ == "__main__":
    uvicorn.run(
        "sse_hello_server:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
