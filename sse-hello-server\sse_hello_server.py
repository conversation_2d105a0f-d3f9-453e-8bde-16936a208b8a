#!/usr/bin/env python3
"""
基于Server-Sent Events (SSE) 的MCP服务器示例
这个服务器展示了如何使用HTTP+SSE传输方式实现MCP协议
包含实时数据流和事件推送功能
"""

import asyncio
import json
import logging
import time
import random
from datetime import datetime
from typing import Any, AsyncGenerator

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.types import Resource, Tool, TextContent

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("sse-hello-mcp-server")

# 创建MCP服务器实例
server = Server("sse-hello-server")

# 全局状态存储
server_stats = {
    "start_time": datetime.now(),
    "requests_count": 0,
    "active_connections": 0,
    "last_activity": datetime.now()
}

# 模拟实时数据源
async def generate_real_time_data() -> AsyncGenerator[dict, None]:
    """生成模拟的实时数据流"""
    while True:
        data = {
            "timestamp": datetime.now().isoformat(),
            "temperature": round(random.uniform(20.0, 30.0), 2),
            "humidity": round(random.uniform(40.0, 80.0), 2),
            "cpu_usage": round(random.uniform(10.0, 90.0), 2),
            "memory_usage": round(random.uniform(30.0, 85.0), 2),
            "random_value": random.randint(1, 100)
        }
        yield data
        await asyncio.sleep(2)  # 每2秒生成一次数据

# MCP 资源定义
@server.list_resources()
async def handle_list_resources() -> list[Resource]:
    """列出可用的资源"""
    return [
        Resource(
            uri="sse://server-stats",
            name="Server Statistics",
            description="Real-time server statistics and metrics",
            mimeType="application/json",
        ),
        Resource(
            uri="sse://real-time-data",
            name="Real-time Data",
            description="Current snapshot of real-time sensor data",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """读取指定的资源"""
    if uri == "sse://server-stats":
        uptime = datetime.now() - server_stats["start_time"]
        stats = {
            "server_name": "SSE Hello MCP Server",
            "version": "1.0.0",
            "uptime_seconds": int(uptime.total_seconds()),
            "uptime_formatted": str(uptime).split('.')[0],
            "requests_count": server_stats["requests_count"],
            "active_connections": server_stats["active_connections"],
            "last_activity": server_stats["last_activity"].isoformat(),
            "transport_type": "HTTP + Server-Sent Events"
        }
        return json.dumps(stats, indent=2, ensure_ascii=False)

    elif uri == "sse://real-time-data":
        data = {
            "timestamp": datetime.now().isoformat(),
            "temperature": round(random.uniform(20.0, 30.0), 2),
            "humidity": round(random.uniform(40.0, 80.0), 2),
            "cpu_usage": round(random.uniform(10.0, 90.0), 2),
            "memory_usage": round(random.uniform(30.0, 85.0), 2),
            "status": "active"
        }
        return json.dumps(data, indent=2, ensure_ascii=False)

    else:
        raise ValueError(f"Unknown resource: {uri}")

# MCP 工具定义
@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="get_system_info",
            description="Get system information for specified component",
            inputSchema={
                "type": "object",
                "properties": {
                    "component": {
                        "type": "string",
                        "description": "Component to query (all, cpu, memory, network, disk)",
                        "default": "all"
                    }
                },
                "required": []
            }
        ),
        Tool(
            name="send_notification",
            description="Send a notification message",
            inputSchema={
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "Notification content"
                    },
                    "priority": {
                        "type": "string",
                        "description": "Priority level (low, normal, high, urgent)",
                        "default": "normal"
                    }
                },
                "required": ["message"]
            }
        ),
        Tool(
            name="calculate_metrics",
            description="Calculate simulated metrics",
            inputSchema={
                "type": "object",
                "properties": {
                    "data_points": {
                        "type": "number",
                        "description": "Number of data points to generate",
                        "default": 10
                    }
                },
                "required": []
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any]) -> list[TextContent]:
    """执行工具调用"""
    server_stats["requests_count"] += 1
    server_stats["last_activity"] = datetime.now()

    if name == "get_system_info":
        component = arguments.get("component", "all")

        if component == "all":
            info = {
                "system": "SSE MCP Server",
                "cpu_cores": 4,
                "memory_total": "8GB",
                "disk_space": "256GB SSD",
                "network": "Ethernet 1Gbps",
                "os": "Python Runtime",
                "mcp_version": "1.10.1"
            }
        elif component == "cpu":
            info = {
                "cpu_usage": f"{random.uniform(10, 90):.1f}%",
                "cpu_cores": 4,
                "cpu_frequency": "2.4GHz"
            }
        elif component == "memory":
            info = {
                "memory_usage": f"{random.uniform(30, 85):.1f}%",
                "memory_total": "8GB",
                "memory_available": f"{random.uniform(1, 5):.1f}GB"
            }
        else:
            info = {"error": f"Unknown component: {component}"}

        return [TextContent(
            type="text",
            text=json.dumps(info, indent=2, ensure_ascii=False)
        )]

    elif name == "send_notification":
        message = arguments.get("message", "")
        priority = arguments.get("priority", "normal")

        notification = {
            "id": f"notif_{int(time.time())}",
            "message": message,
            "priority": priority,
            "timestamp": datetime.now().isoformat(),
            "status": "sent"
        }

        return [TextContent(
            type="text",
            text=f"通知已发送: {json.dumps(notification, indent=2, ensure_ascii=False)}"
        )]

    elif name == "calculate_metrics":
        data_points = arguments.get("data_points", 10)

        # 生成模拟数据
        values = [random.uniform(0, 100) for _ in range(data_points)]

        metrics = {
            "data_points": data_points,
            "values": [round(v, 2) for v in values],
            "statistics": {
                "mean": round(sum(values) / len(values), 2),
                "min": round(min(values), 2),
                "max": round(max(values), 2),
                "range": round(max(values) - min(values), 2)
            },
            "generated_at": datetime.now().isoformat()
        }

        return [TextContent(
            type="text",
            text=json.dumps(metrics, indent=2, ensure_ascii=False)
        )]

    else:
        raise ValueError(f"Unknown tool: {name}")

# 简单的HTTP服务器实现
import http.server
import socketserver
import threading
import urllib.parse

class SSEHandler(http.server.BaseHTTPRequestHandler):
    """处理HTTP请求的处理器"""

    def do_GET(self):
        """处理GET请求"""
        if self.path == "/":
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            response = {
                "name": "SSE Hello MCP Server",
                "version": "1.0.0",
                "transport": "HTTP + Server-Sent Events",
                "endpoints": {
                    "sse_stream": "/stream",
                    "health": "/health"
                },
                "uptime": str(datetime.now() - server_stats["start_time"]).split('.')[0]
            }
            self.wfile.write(json.dumps(response, indent=2).encode())

        elif self.path == "/health":
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()

            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "uptime": str(datetime.now() - server_stats["start_time"]).split('.')[0]
            }
            self.wfile.write(json.dumps(response, indent=2).encode())

        elif self.path == "/stream":
            self.send_response(200)
            self.send_header('Content-type', 'text/event-stream')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'keep-alive')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            # 发送SSE数据流
            try:
                server_stats["active_connections"] += 1
                count = 0
                while count < 10:  # 限制发送10次数据
                    data = {
                        "timestamp": datetime.now().isoformat(),
                        "temperature": round(random.uniform(20.0, 30.0), 2),
                        "humidity": round(random.uniform(40.0, 80.0), 2),
                        "cpu_usage": round(random.uniform(10.0, 90.0), 2),
                        "memory_usage": round(random.uniform(30.0, 85.0), 2),
                        "count": count + 1
                    }

                    event_data = f"event: data_update\ndata: {json.dumps(data)}\n\n"
                    self.wfile.write(event_data.encode())
                    self.wfile.flush()

                    count += 1
                    time.sleep(2)

            except Exception as e:
                logger.error(f"SSE stream error: {e}")
            finally:
                server_stats["active_connections"] -= 1
        else:
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        """自定义日志格式"""
        logger.info(f"{self.address_string()} - {format % args}")

async def run_mcp_server():
    """运行MCP服务器"""
    from mcp.server.stdio import stdio_server

    logger.info("Starting MCP Server...")
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="sse-hello-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

def run_http_server():
    """运行HTTP服务器"""
    PORT = 8000
    with socketserver.TCPServer(("", PORT), SSEHandler) as httpd:
        logger.info(f"HTTP Server running at http://localhost:{PORT}")
        logger.info("Available endpoints:")
        logger.info("  - http://localhost:8000/ (Server info)")
        logger.info("  - http://localhost:8000/health (Health check)")
        logger.info("  - http://localhost:8000/stream (SSE data stream)")
        httpd.serve_forever()

if __name__ == "__main__":
    print("SSE Hello MCP Server")
    print("===================")
    print("This server demonstrates MCP with HTTP+SSE transport.")
    print()
    print("Choose an option:")
    print("1. Run as MCP Server (STDIO)")
    print("2. Run as HTTP Server with SSE")
    print("3. Run both (HTTP server in background)")

    choice = input("Enter your choice (1-3): ").strip()

    if choice == "1":
        asyncio.run(run_mcp_server())
    elif choice == "2":
        run_http_server()
    elif choice == "3":
        # 在后台运行HTTP服务器
        http_thread = threading.Thread(target=run_http_server, daemon=True)
        http_thread.start()

        # 在前台运行MCP服务器
        asyncio.run(run_mcp_server())
    else:
        print("Invalid choice. Running as MCP Server (STDIO)...")
        asyncio.run(run_mcp_server())
