#!/usr/bin/env python3
"""
符合MCP协议标准的服务器实现
支持字符串处理功能和用户会话管理
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime
from typing import Any, Dict, List

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    LoggingLevel
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("proper-mcp-server")

# 创建MCP服务器实例
server = Server("string-processor-mcp")

# 用户会话管理
class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, Dict[str, Any]] = {}
    
    def create_session(self, user_id: str = None) -> str:
        """创建新会话"""
        session_id = user_id or str(uuid.uuid4())[:8]
        self.sessions[session_id] = {
            "created_at": datetime.now(),
            "last_activity": datetime.now(),
            "message_history": [],
            "user_preferences": {},
            "statistics": {
                "total_requests": 0,
                "reverse_count": 0,
                "uppercase_count": 0,
                "lowercase_count": 0
            }
        }
        logger.info(f"创建会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        if session_id not in self.sessions:
            return self.create_session(session_id)
        return self.sessions[session_id]
    
    def update_activity(self, session_id: str):
        """更新会话活动"""
        if session_id in self.sessions:
            self.sessions[session_id]["last_activity"] = datetime.now()
    
    def add_to_history(self, session_id: str, operation: str, input_text: str, output_text: str):
        """添加操作历史"""
        session = self.get_session(session_id)
        session["message_history"].append({
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "input": input_text,
            "output": output_text
        })
        session["statistics"]["total_requests"] += 1
        if operation == "reverse":
            session["statistics"]["reverse_count"] += 1
        elif operation == "uppercase":
            session["statistics"]["uppercase_count"] += 1
        elif operation == "lowercase":
            session["statistics"]["lowercase_count"] += 1

# 全局会话管理器
session_manager = SessionManager()

# MCP 资源定义
@server.list_resources()
async def handle_list_resources() -> List[Resource]:
    """列出可用的资源"""
    return [
        Resource(
            uri="mcp://sessions/list",
            name="Active Sessions",
            description="List of all active user sessions",
            mimeType="application/json",
        ),
        Resource(
            uri="mcp://sessions/{session_id}",
            name="Session Details",
            description="Detailed information about a specific session",
            mimeType="application/json",
        ),
        Resource(
            uri="mcp://server/info",
            name="Server Information",
            description="Information about this MCP server",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """读取指定的资源"""
    if uri == "mcp://sessions/list":
        sessions_info = {}
        for session_id, session_data in session_manager.sessions.items():
            sessions_info[session_id] = {
                "created_at": session_data["created_at"].isoformat(),
                "last_activity": session_data["last_activity"].isoformat(),
                "total_requests": session_data["statistics"]["total_requests"]
            }
        return json.dumps({
            "active_sessions": len(session_manager.sessions),
            "sessions": sessions_info
        }, indent=2, ensure_ascii=False)
    
    elif uri.startswith("mcp://sessions/"):
        session_id = uri.split("/")[-1]
        session = session_manager.get_session(session_id)
        # 转换datetime对象为字符串
        session_copy = session.copy()
        session_copy["created_at"] = session["created_at"].isoformat()
        session_copy["last_activity"] = session["last_activity"].isoformat()
        return json.dumps(session_copy, indent=2, ensure_ascii=False)
    
    elif uri == "mcp://server/info":
        return json.dumps({
            "name": "String Processor MCP Server",
            "version": "1.0.0",
            "description": "A MCP server for string processing with session management",
            "capabilities": [
                "String reversal",
                "Case conversion",
                "Session management",
                "Operation history"
            ],
            "author": "MCP Learning Project",
            "created_at": datetime.now().isoformat()
        }, indent=2, ensure_ascii=False)
    
    else:
        raise ValueError(f"Unknown resource: {uri}")

# MCP 工具定义
@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="reverse_string",
            description="Reverse a string with session tracking",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "The text to reverse"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Optional session ID for tracking (auto-generated if not provided)"
                    }
                },
                "required": ["text"]
            }
        ),
        Tool(
            name="convert_case",
            description="Convert string case (upper/lower) with session tracking",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "The text to convert"
                    },
                    "case": {
                        "type": "string",
                        "enum": ["upper", "lower", "title"],
                        "description": "Target case: upper, lower, or title"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Optional session ID for tracking"
                    }
                },
                "required": ["text", "case"]
            }
        ),
        Tool(
            name="get_session_stats",
            description="Get statistics for a user session",
            inputSchema={
                "type": "object",
                "properties": {
                    "session_id": {
                        "type": "string",
                        "description": "Session ID to get stats for"
                    }
                },
                "required": ["session_id"]
            }
        ),
        Tool(
            name="process_batch",
            description="Process multiple strings in batch with the same operation",
            inputSchema={
                "type": "object",
                "properties": {
                    "texts": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Array of texts to process"
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["reverse", "upper", "lower", "title"],
                        "description": "Operation to apply to all texts"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Optional session ID for tracking"
                    }
                },
                "required": ["texts", "operation"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """执行工具调用"""
    
    if name == "reverse_string":
        text = arguments.get("text", "")
        session_id = arguments.get("session_id") or session_manager.create_session()
        
        if not text:
            return [TextContent(
                type="text",
                text="错误: 输入文本不能为空"
            )]
        
        # 执行字符串反转
        reversed_text = text[::-1]
        
        # 更新会话
        session_manager.update_activity(session_id)
        session_manager.add_to_history(session_id, "reverse", text, reversed_text)
        
        result = {
            "operation": "reverse_string",
            "session_id": session_id,
            "input": text,
            "output": reversed_text,
            "timestamp": datetime.now().isoformat()
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(result, ensure_ascii=False, indent=2)
        )]
    
    elif name == "convert_case":
        text = arguments.get("text", "")
        case = arguments.get("case", "upper")
        session_id = arguments.get("session_id") or session_manager.create_session()
        
        if not text:
            return [TextContent(
                type="text",
                text="错误: 输入文本不能为空"
            )]
        
        # 执行大小写转换
        if case == "upper":
            converted_text = text.upper()
        elif case == "lower":
            converted_text = text.lower()
        elif case == "title":
            converted_text = text.title()
        else:
            return [TextContent(
                type="text",
                text=f"错误: 不支持的大小写类型 '{case}'"
            )]
        
        # 更新会话
        session_manager.update_activity(session_id)
        session_manager.add_to_history(session_id, case, text, converted_text)
        
        result = {
            "operation": f"convert_case_{case}",
            "session_id": session_id,
            "input": text,
            "output": converted_text,
            "timestamp": datetime.now().isoformat()
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(result, ensure_ascii=False, indent=2)
        )]
    
    elif name == "get_session_stats":
        session_id = arguments.get("session_id")
        
        if not session_id:
            return [TextContent(
                type="text",
                text="错误: 需要提供session_id"
            )]
        
        session = session_manager.get_session(session_id)
        
        stats = {
            "session_id": session_id,
            "created_at": session["created_at"].isoformat(),
            "last_activity": session["last_activity"].isoformat(),
            "statistics": session["statistics"],
            "history_count": len(session["message_history"]),
            "recent_operations": session["message_history"][-5:] if session["message_history"] else []
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(stats, ensure_ascii=False, indent=2)
        )]
    
    elif name == "process_batch":
        texts = arguments.get("texts", [])
        operation = arguments.get("operation", "reverse")
        session_id = arguments.get("session_id") or session_manager.create_session()
        
        if not texts:
            return [TextContent(
                type="text",
                text="错误: 需要提供要处理的文本数组"
            )]
        
        results = []
        for text in texts:
            if operation == "reverse":
                processed = text[::-1]
            elif operation == "upper":
                processed = text.upper()
            elif operation == "lower":
                processed = text.lower()
            elif operation == "title":
                processed = text.title()
            else:
                processed = f"错误: 不支持的操作 '{operation}'"
            
            results.append({
                "input": text,
                "output": processed
            })
            
            # 更新会话历史
            session_manager.add_to_history(session_id, operation, text, processed)
        
        session_manager.update_activity(session_id)
        
        batch_result = {
            "operation": f"batch_{operation}",
            "session_id": session_id,
            "processed_count": len(texts),
            "results": results,
            "timestamp": datetime.now().isoformat()
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(batch_result, ensure_ascii=False, indent=2)
        )]
    
    else:
        return [TextContent(
            type="text",
            text=f"错误: 未知工具 '{name}'"
        )]

async def main_stdio():
    """STDIO模式 - 用于本地MCP客户端连接"""
    logger.info("启动 String Processor MCP Server (STDIO模式)...")

    # 使用stdio传输运行服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="string-processor-mcp",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

async def main_http():
    """HTTP模式 - 用于远程访问"""
    try:
        import uvicorn
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware

        logger.info("启动 String Processor MCP Server (HTTP模式)...")

        # 创建FastAPI应用
        app = FastAPI(
            title="String Processor MCP Server",
            description="A MCP server for string processing with session management",
            version="1.0.0"
        )

        # 添加CORS中间件支持远程访问
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        @app.get("/")
        async def root():
            """根端点 - 服务器信息"""
            return {
                "name": "String Processor MCP Server",
                "version": "1.0.0",
                "description": "A MCP server for string processing with session management",
                "mode": "HTTP",
                "endpoints": {
                    "mcp": "/mcp",
                    "health": "/health",
                    "docs": "/docs"
                },
                "capabilities": [
                    "String reversal",
                    "Case conversion",
                    "Session management",
                    "Remote access"
                ],
                "active_sessions": len(session_manager.sessions),
                "timestamp": datetime.now().isoformat()
            }

        @app.get("/health")
        async def health():
            """健康检查"""
            return {
                "status": "healthy",
                "active_sessions": len(session_manager.sessions),
                "timestamp": datetime.now().isoformat()
            }

        @app.post("/mcp")
        async def mcp_endpoint(request: dict):
            """MCP协议端点"""
            try:
                # 这是一个简化的MCP实现
                method = request.get("method")
                params = request.get("params", {})
                request_id = request.get("id")

                if method == "initialize":
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {
                                "tools": {},
                                "resources": {}
                            },
                            "serverInfo": {
                                "name": "string-processor-mcp",
                                "version": "1.0.0"
                            }
                        }
                    }

                elif method == "tools/list":
                    tools = await handle_list_tools()
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "tools": [tool.model_dump() for tool in tools]
                        }
                    }

                elif method == "tools/call":
                    name = params.get("name")
                    arguments = params.get("arguments", {})
                    result = await handle_call_tool(name, arguments)
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "content": [content.model_dump() for content in result]
                        }
                    }

                elif method == "resources/list":
                    resources = await handle_list_resources()
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "resources": [resource.model_dump() for resource in resources]
                        }
                    }

                elif method == "resources/read":
                    uri = params.get("uri")
                    content = await handle_read_resource(uri)
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "contents": [{
                                "uri": uri,
                                "mimeType": "application/json",
                                "text": content
                            }]
                        }
                    }

                else:
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "error": {
                            "code": -32601,
                            "message": f"Method not found: {method}"
                        }
                    }

            except Exception as e:
                logger.error(f"MCP请求处理错误: {e}")
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32603,
                        "message": f"Internal error: {str(e)}"
                    }
                }

        # 启动HTTP服务器
        config = uvicorn.Config(
            app,
            host="0.0.0.0",  # 允许远程访问
            port=8002,
            log_level="info"
        )
        server_instance = uvicorn.Server(config)

        logger.info("🌐 HTTP MCP服务器启动在: http://0.0.0.0:8002")
        logger.info("🔗 远程访问地址: http://<your-ip>:8002")
        logger.info("📡 MCP端点: http://<your-ip>:8002/mcp")
        logger.info("📖 API文档: http://<your-ip>:8002/docs")

        await server_instance.serve()

    except ImportError as e:
        logger.error(f"HTTP模式需要安装额外依赖: pip install fastapi uvicorn")
        logger.error(f"缺少模块: {e}")
        logger.info("回退到STDIO模式...")
        await main_stdio()
    except Exception as e:
        logger.error(f"HTTP服务器启动失败: {e}")
        logger.info("回退到STDIO模式...")
        await main_stdio()

def show_menu():
    """显示启动菜单"""
    print("=" * 60)
    print("🌟 String Processor MCP Server")
    print("=" * 60)
    print("这是一个符合MCP协议标准的字符串处理服务器")
    print("支持字符串反转、大小写转换和会话管理功能")
    print("=" * 60)
    print()
    print("请选择运行模式:")
    print("1. STDIO模式 (本地MCP客户端连接)")
    print("2. HTTP模式 (支持远程访问)")
    print("3. 退出")
    print()

async def main():
    """主函数"""
    show_menu()

    try:
        choice = input("请输入选择 (1-3): ").strip()

        if choice == "1":
            await main_stdio()
        elif choice == "2":
            await main_http()
        elif choice == "3":
            print("再见！")
            return
        else:
            print("无效选择，默认使用STDIO模式...")
            await main_stdio()

    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        logger.error(f"启动失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
