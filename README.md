# MCP 开发环境

这是一个用于学习和开发 Model Context Protocol (MCP) 服务器的环境。

## 环境信息

- **Conda环境名称**: `mcp-dev`
- **Python版本**: 3.11.13
- **MCP版本**: 1.10.1

## 快速开始

### 1. 激活环境

有两种方式激活MCP开发环境：

**方式一：使用批处理文件（推荐）**
```bash
# 双击运行或在命令行中执行
activate_mcp_env.bat
```

**方式二：使用PowerShell脚本**
```powershell
# 在PowerShell中执行
.\activate_mcp_env.ps1
```

**方式三：手动激活**
```bash
conda activate mcp-dev
```

### 2. 验证安装

激活环境后，可以验证MCP是否正确安装：

```bash
python -c "import mcp; print('MCP installed successfully')"
pip show mcp
```

### 3. 运行示例服务器

我们提供了一个简单的MCP服务器示例：

```bash
python hello_mcp_server.py
```

## 示例服务器功能

`hello_mcp_server.py` 包含以下功能：

### 资源 (Resources)
- `hello://greeting` - 简单的问候消息
- `hello://info` - 服务器信息

### 工具 (Tools)
- `say_hello` - 向指定的人问好
- `add_numbers` - 计算两个数字的和

## 下一步学习

1. **理解MCP基础概念**
   - Resources: 服务器提供的数据
   - Tools: 客户端可以调用的功能
   - Prompts: 预定义的提示模板

2. **修改示例服务器**
   - 添加新的工具
   - 创建新的资源
   - 实现更复杂的功能

3. **创建自己的MCP服务器**
   - 文件系统操作
   - 数据库连接
   - API集成
   - 自定义业务逻辑

## 有用的命令

```bash
# 查看已安装的包
pip list

# 安装额外的依赖
pip install <package-name>

# 查看MCP文档
python -c "import mcp; help(mcp)"

# 退出环境
conda deactivate
```

## 资源链接

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [MCP 规范](https://spec.modelcontextprotocol.io/)

## 故障排除

如果遇到问题：

1. 确保conda环境已正确激活
2. 检查Python和MCP版本
3. 重新安装MCP: `pip uninstall mcp && pip install mcp`
4. 查看错误日志获取详细信息

祝你学习愉快！🚀
