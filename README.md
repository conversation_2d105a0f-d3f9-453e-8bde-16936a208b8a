# MCP 开发环境

这是一个用于学习和开发 Model Context Protocol (MCP) 服务器的综合环境，包含多种传输方式的示例实现。

## 🚀 项目概述

本项目提供了完整的MCP学习环境，包含：
- **开发环境配置** - 预配置的conda环境和激活脚本
- **多种传输方式示例** - STDIO和HTTP+SSE两种实现
- **详细文档** - 每个示例都有完整的说明和使用指南
- **最佳实践** - 展示MCP服务器开发的标准模式

## 📋 Model Context Protocol (MCP) 简介

Model Context Protocol (MCP) 是一个开放标准，用于AI助手与外部数据源和工具的安全连接。MCP使AI应用能够：

- **访问资源** - 读取文件、数据库记录、API响应等
- **调用工具** - 执行函数、运行命令、处理数据
- **使用提示** - 获取预定义的提示模板和指令

### 核心概念
- **Resources** - 服务器提供的数据和内容
- **Tools** - 客户端可以调用的功能和操作
- **Prompts** - 预定义的提示模板
- **Transports** - 通信传输方式（STDIO、HTTP、SSE等）

## 🏗️ 项目结构

```
mcp/
├── README.md                    # 主项目文档
├── activate_mcp_env.bat         # Windows环境激活脚本
├── activate_mcp_env.ps1         # PowerShell环境激活脚本
├── stdio-hello-server/          # STDIO传输示例
│   ├── hello_mcp_server.py      # STDIO MCP服务器
│   └── README.md                # STDIO服务器文档
└── sse-hello-server/            # HTTP+SSE传输示例
    ├── sse_hello_server.py      # SSE MCP服务器
    └── README.md                # SSE服务器文档
```

## 🔧 环境信息

- **Conda环境名称**: `mcp-dev`
- **Python版本**: 3.11.13
- **MCP版本**: 1.10.1
- **支持的传输方式**: STDIO, HTTP+SSE

## ⚡ 快速开始

### 1. 激活开发环境

选择以下任一方式激活MCP开发环境：

**方式一：批处理文件（Windows推荐）**
```bash
# 双击运行或在命令行中执行
activate_mcp_env.bat
```

**方式二：PowerShell脚本**
```powershell
# 在PowerShell中执行
.\activate_mcp_env.ps1
```

**方式三：手动激活**
```bash
conda activate mcp-dev
```

### 2. 验证环境

激活环境后，验证MCP是否正确安装：

```bash
python -c "import mcp; print('MCP installed successfully')"
pip show mcp
```

### 3. 选择服务器示例

根据你的需求选择合适的服务器示例：

#### STDIO服务器（推荐入门）
```bash
cd stdio-hello-server
python hello_mcp_server.py
```

#### HTTP+SSE服务器（Web应用）
```bash
cd sse-hello-server
pip install fastapi uvicorn sse-starlette  # 安装额外依赖
python sse_hello_server.py
```

## 📚 服务器示例对比

| 特性 | STDIO服务器 | HTTP+SSE服务器 |
|------|-------------|----------------|
| **传输方式** | 标准输入输出 | HTTP + Server-Sent Events |
| **适用场景** | 命令行工具、进程间通信 | Web应用、浏览器集成 |
| **实时数据** | 请求-响应模式 | 实时数据流推送 |
| **Web兼容** | ❌ | ✅ |
| **复杂度** | 简单 | 中等 |
| **依赖项** | 仅MCP | FastAPI, Uvicorn, SSE-Starlette |
| **调试方式** | 命令行 | 浏览器 + 开发者工具 |
| **推荐用途** | 学习MCP基础 | Web集成、实时应用 |

## 🎯 示例服务器功能

### STDIO Hello Server
- **资源**: 问候消息、服务器信息
- **工具**: 个性化问候、数字计算
- **特点**: 简单易懂，适合入门学习

### SSE Hello Server
- **资源**: 服务器统计、实时数据快照
- **工具**: 系统信息查询、通知发送、指标计算
- **特点**: 实时数据流、Web界面、高级功能

## 🛠️ 开发指南

### 选择合适的传输方式

**选择STDIO当你需要：**
- 学习MCP基础概念
- 开发命令行工具
- 简单的进程间通信
- 最小化依赖

**选择HTTP+SSE当你需要：**
- Web应用集成
- 实时数据推送
- 浏览器兼容性
- RESTful API接口

### 开发流程建议

1. **从STDIO开始** - 理解MCP核心概念
2. **学习资源和工具** - 掌握基本功能实现
3. **尝试SSE服务器** - 体验Web集成
4. **自定义开发** - 创建你自己的MCP服务器

## 📖 学习路径

### 初学者路径
1. 阅读MCP协议简介
2. 运行STDIO示例服务器
3. 修改工具和资源
4. 理解JSON-RPC通信

### 进阶路径
1. 部署SSE服务器
2. 集成外部API
3. 实现认证机制
4. 优化性能和错误处理

### 高级路径
1. 自定义传输协议
2. 多客户端支持
3. 分布式MCP架构
4. 生产环境部署

## 🔧 常用命令

```bash
# 环境管理
conda activate mcp-dev          # 激活环境
conda deactivate               # 退出环境
pip list                       # 查看已安装包

# 开发工具
python -m py_compile file.py   # 语法检查
python -c "import mcp; help(mcp)"  # 查看MCP文档

# 服务器测试
curl http://localhost:8000/health  # 测试SSE服务器
```

## 🌐 相关资源

### 官方文档
- [MCP 官方网站](https://modelcontextprotocol.io/)
- [MCP 协议规范](https://spec.modelcontextprotocol.io/)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)

### 社区资源
- [MCP 示例集合](https://github.com/modelcontextprotocol/servers)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [Server-Sent Events 规范](https://html.spec.whatwg.org/multipage/server-sent-events.html)

## 🐛 故障排除

### 环境问题
- **conda环境未激活**: 检查命令提示符前缀
- **MCP包未安装**: 运行 `pip install mcp`
- **Python版本不兼容**: 确保使用Python 3.11+

### 服务器问题
- **STDIO服务器无响应**: 检查JSON-RPC消息格式
- **SSE服务器启动失败**: 确认端口8000未被占用
- **依赖包缺失**: 按照各服务器README安装依赖

### 调试技巧
1. 启用详细日志记录
2. 使用浏览器开发者工具（SSE服务器）
3. 检查网络连接和防火墙设置
4. 验证JSON消息格式

---

🎉 **开始你的MCP开发之旅！** 选择一个示例服务器，按照文档开始探索MCP的强大功能。
