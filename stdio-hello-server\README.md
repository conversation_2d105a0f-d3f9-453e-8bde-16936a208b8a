# STDIO Hello MCP Server

这是一个基于标准输入输出（STDIO）的简单MCP服务器示例，用于学习MCP协议的基本概念和实现。

## 功能特性

### 资源 (Resources)
- **hello://greeting** - 提供简单的问候消息
- **hello://info** - 显示服务器的基本信息

### 工具 (Tools)
- **say_hello** - 向指定的人问好
  - 参数：`name` (string) - 要问候的人的姓名
  - 返回：个性化的问候消息

- **add_numbers** - 计算两个数字的和
  - 参数：`a` (number), `b` (number) - 要相加的两个数字
  - 返回：计算结果

## 技术实现

### 通信方式
- **传输协议**: STDIO (标准输入输出)
- **消息格式**: JSON-RPC 2.0
- **连接方式**: 进程间通信

### 依赖项
- Python 3.11+
- mcp 包 (>=1.10.1)

## 安装和运行

### 1. 激活MCP环境
```bash
# 从项目根目录运行
conda activate mcp-dev
```

### 2. 运行服务器
```bash
cd stdio-hello-server
python hello_mcp_server.py
```

### 3. 测试服务器
服务器启动后，它会等待来自MCP客户端的JSON-RPC消息。你可以通过以下方式测试：

#### 测试资源列表
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "resources/list"
}
```

#### 测试工具调用
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/call",
  "params": {
    "name": "say_hello",
    "arguments": {
      "name": "Alice"
    }
  }
}
```

## 代码结构

```python
# 主要组件
server = Server("hello-mcp-server")  # 创建服务器实例

@server.list_resources()           # 资源列表处理器
@server.read_resource()            # 资源读取处理器
@server.list_tools()               # 工具列表处理器
@server.call_tool()                # 工具调用处理器
```

## 学习要点

1. **MCP服务器基础结构** - 了解如何创建和配置MCP服务器
2. **资源管理** - 学习如何定义和提供资源
3. **工具实现** - 掌握工具的定义和执行逻辑
4. **STDIO通信** - 理解基于标准输入输出的通信机制
5. **JSON-RPC协议** - 熟悉MCP使用的消息格式

## 扩展建议

1. **添加更多工具**
   - 文件操作工具
   - 数据处理工具
   - 网络请求工具

2. **增强资源功能**
   - 动态资源生成
   - 文件系统资源
   - 数据库资源

3. **错误处理**
   - 输入验证
   - 异常处理
   - 日志记录

4. **性能优化**
   - 异步处理
   - 缓存机制
   - 连接池

## 故障排除

### 常见问题

1. **服务器无法启动**
   - 检查Python环境是否正确激活
   - 确认MCP包已正确安装

2. **工具调用失败**
   - 验证输入参数格式
   - 检查参数类型和必需字段

3. **资源访问错误**
   - 确认资源URI格式正确
   - 检查资源是否已正确注册

### 调试技巧

1. 启用详细日志：
```python
logging.basicConfig(level=logging.DEBUG)
```

2. 添加调试输出：
```python
logger.info(f"Received tool call: {name} with args: {arguments}")
```

## 相关资源

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [JSON-RPC 2.0 规范](https://www.jsonrpc.org/specification)
