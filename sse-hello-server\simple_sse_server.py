#!/usr/bin/env python3
"""
简化的SSE服务器 - 直接启动HTTP模式
"""

import json
import logging
import time
import random
import http.server
import socketserver
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("simple-sse-server")

# 全局状态存储
server_stats = {
    "start_time": datetime.now(),
    "requests_count": 0,
    "active_connections": 0,
    "last_activity": datetime.now()
}

def generate_data_point() -> dict:
    """生成单个数据点"""
    return {
        "timestamp": datetime.now().isoformat(),
        "temperature": round(random.uniform(20.0, 30.0), 2),
        "humidity": round(random.uniform(40.0, 80.0), 2),
        "cpu_usage": round(random.uniform(10.0, 90.0), 2),
        "memory_usage": round(random.uniform(30.0, 85.0), 2),
        "random_value": random.randint(1, 100)
    }

class SSEHandler(http.server.BaseHTTPRequestHandler):
    """处理HTTP请求的处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == "/":
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                "name": "Simple SSE Server",
                "version": "1.0.0",
                "transport": "HTTP + Server-Sent Events",
                "endpoints": {
                    "sse_stream": "/stream",
                    "health": "/health"
                },
                "uptime": str(datetime.now() - server_stats["start_time"]).split('.')[0],
                "description": "访问 /stream 查看实时数据流"
            }
            self.wfile.write(json.dumps(response, indent=2, ensure_ascii=False).encode('utf-8'))
            
        elif self.path == "/health":
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "uptime": str(datetime.now() - server_stats["start_time"]).split('.')[0],
                "active_connections": server_stats["active_connections"],
                "total_requests": server_stats["requests_count"]
            }
            self.wfile.write(json.dumps(response, indent=2, ensure_ascii=False).encode('utf-8'))
            
        elif self.path == "/stream":
            self.send_response(200)
            self.send_header('Content-type', 'text/event-stream')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'keep-alive')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            # 发送SSE数据流
            try:
                server_stats["active_connections"] += 1
                server_stats["requests_count"] += 1
                
                # 发送欢迎消息
                welcome_msg = {
                    "message": "欢迎连接到SSE数据流！",
                    "timestamp": datetime.now().isoformat(),
                    "info": "每2秒会推送一次模拟的传感器数据"
                }
                event_data = f"event: welcome\ndata: {json.dumps(welcome_msg, ensure_ascii=False)}\n\n"
                self.wfile.write(event_data.encode('utf-8'))
                self.wfile.flush()
                
                count = 0
                while count < 20:  # 发送20次数据
                    # 使用数据生成函数
                    data = generate_data_point()
                    data["count"] = count + 1  # 添加计数器
                    data["message"] = f"这是第 {count + 1} 次数据推送"
                    
                    event_data = f"event: data_update\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"
                    self.wfile.write(event_data.encode('utf-8'))
                    self.wfile.flush()
                    
                    count += 1
                    time.sleep(2)
                
                # 发送结束消息
                end_msg = {
                    "message": "数据流结束，感谢观看！",
                    "timestamp": datetime.now().isoformat(),
                    "total_sent": count
                }
                event_data = f"event: end\ndata: {json.dumps(end_msg, ensure_ascii=False)}\n\n"
                self.wfile.write(event_data.encode('utf-8'))
                self.wfile.flush()
                    
            except Exception as e:
                logger.error(f"SSE stream error: {e}")
            finally:
                server_stats["active_connections"] -= 1
                
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            error_response = {
                "error": "Not Found",
                "message": f"路径 {self.path} 不存在",
                "available_paths": ["/", "/health", "/stream"]
            }
            self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        logger.info(f"{self.address_string()} - {format % args}")

def run_http_server():
    """运行HTTP服务器"""
    PORT = 8000
    try:
        with socketserver.TCPServer(("", PORT), SSEHandler) as httpd:
            logger.info(f"🚀 SSE服务器启动成功！")
            logger.info(f"📡 服务器地址: http://localhost:{PORT}")
            logger.info(f"🌐 可用端点:")
            logger.info(f"   - http://localhost:{PORT}/ (服务器信息)")
            logger.info(f"   - http://localhost:{PORT}/health (健康检查)")
            logger.info(f"   - http://localhost:{PORT}/stream (SSE数据流)")
            logger.info(f"💡 在浏览器中访问上述地址来测试SSE功能")
            logger.info(f"⏹️  按 Ctrl+C 停止服务器")
            httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("🌟 简化版 SSE Hello Server")
    print("=" * 50)
    run_http_server()
