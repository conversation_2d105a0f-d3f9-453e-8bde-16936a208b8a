#!/usr/bin/env python3
"""
交互式SSE MCP服务器
支持用户会话隔离和双向通信
"""

import json
import logging
import time
import uuid
import threading
import queue
import http.server
import socketserver
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("interactive-sse-server")

# 全局会话管理
class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.message_queues: Dict[str, queue.Queue] = {}
        self.lock = threading.Lock()
    
    def create_session(self) -> str:
        """创建新会话"""
        session_id = str(uuid.uuid4())[:8]  # 短ID便于调试
        with self.lock:
            self.sessions[session_id] = {
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "message_count": 0,
                "user_data": {}
            }
            self.message_queues[session_id] = queue.Queue()
        logger.info(f"创建新会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        with self.lock:
            return self.sessions.get(session_id, {})
    
    def update_activity(self, session_id: str):
        """更新会话活动时间"""
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id]["last_activity"] = datetime.now()
    
    def add_message(self, session_id: str, message: Dict[str, Any]):
        """向会话添加消息"""
        if session_id in self.message_queues:
            self.message_queues[session_id].put(message)
            with self.lock:
                if session_id in self.sessions:
                    self.sessions[session_id]["message_count"] += 1
    
    def get_message(self, session_id: str, timeout: float = 1.0) -> Dict[str, Any]:
        """从会话获取消息（阻塞）"""
        if session_id in self.message_queues:
            try:
                return self.message_queues[session_id].get(timeout=timeout)
            except queue.Empty:
                return None
        return None
    
    def cleanup_old_sessions(self, max_age_minutes: int = 30):
        """清理过期会话"""
        current_time = datetime.now()
        with self.lock:
            expired_sessions = []
            for session_id, session_data in self.sessions.items():
                age = (current_time - session_data["last_activity"]).total_seconds() / 60
                if age > max_age_minutes:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                del self.sessions[session_id]
                if session_id in self.message_queues:
                    del self.message_queues[session_id]
                logger.info(f"清理过期会话: {session_id}")

# 全局会话管理器
session_manager = SessionManager()

# 消息处理器
class MessageProcessor:
    @staticmethod
    def process_message(session_id: str, user_input: str) -> Dict[str, Any]:
        """处理用户消息并返回响应"""
        session = session_manager.get_session(session_id)
        
        # 解析用户输入
        user_input = user_input.strip()
        
        if not user_input:
            return {
                "type": "error",
                "message": "输入不能为空",
                "timestamp": datetime.now().isoformat()
            }
        
        # 不同的命令处理
        if user_input.startswith("/"):
            return MessageProcessor._handle_command(session_id, user_input)
        else:
            return MessageProcessor._handle_text(session_id, user_input)
    
    @staticmethod
    def _handle_command(session_id: str, command: str) -> Dict[str, Any]:
        """处理命令"""
        parts = command.split()
        cmd = parts[0].lower()
        
        if cmd == "/help":
            return {
                "type": "help",
                "message": "可用命令:\n/help - 显示帮助\n/reverse <文本> - 反转文本\n/upper <文本> - 转大写\n/count - 显示消息计数\n/session - 显示会话信息",
                "timestamp": datetime.now().isoformat()
            }
        
        elif cmd == "/reverse":
            if len(parts) > 1:
                text = " ".join(parts[1:])
                reversed_text = text[::-1]
                return {
                    "type": "result",
                    "operation": "reverse",
                    "input": text,
                    "output": reversed_text,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "type": "error",
                    "message": "用法: /reverse <要反转的文本>",
                    "timestamp": datetime.now().isoformat()
                }
        
        elif cmd == "/upper":
            if len(parts) > 1:
                text = " ".join(parts[1:])
                upper_text = text.upper()
                return {
                    "type": "result",
                    "operation": "uppercase",
                    "input": text,
                    "output": upper_text,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "type": "error",
                    "message": "用法: /upper <要转换的文本>",
                    "timestamp": datetime.now().isoformat()
                }
        
        elif cmd == "/count":
            session = session_manager.get_session(session_id)
            return {
                "type": "info",
                "message": f"当前会话消息计数: {session.get('message_count', 0)}",
                "timestamp": datetime.now().isoformat()
            }
        
        elif cmd == "/session":
            session = session_manager.get_session(session_id)
            return {
                "type": "info",
                "session_id": session_id,
                "created_at": session.get("created_at", "").isoformat() if session.get("created_at") else "",
                "message_count": session.get("message_count", 0),
                "timestamp": datetime.now().isoformat()
            }
        
        else:
            return {
                "type": "error",
                "message": f"未知命令: {cmd}。输入 /help 查看可用命令。",
                "timestamp": datetime.now().isoformat()
            }
    
    @staticmethod
    def _handle_text(session_id: str, text: str) -> Dict[str, Any]:
        """处理普通文本"""
        # 默认行为：反转文本
        reversed_text = text[::-1]
        return {
            "type": "result",
            "operation": "auto_reverse",
            "input": text,
            "output": reversed_text,
            "message": "自动反转文本（输入 /help 查看更多命令）",
            "timestamp": datetime.now().isoformat()
        }

class InteractiveSSEHandler(http.server.BaseHTTPRequestHandler):
    """交互式SSE处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query_params = parse_qs(parsed_url.query)
        
        if path == "/":
            self._serve_homepage()
        elif path == "/health":
            self._serve_health()
        elif path == "/stream":
            session_id = query_params.get("session_id", [None])[0]
            if not session_id:
                session_id = session_manager.create_session()
            self._serve_sse_stream(session_id)
        elif path == "/demo":
            self._serve_demo_page()
        else:
            self._serve_404()
    
    def do_POST(self):
        """处理POST请求（用户输入）"""
        if self.path == "/send":
            self._handle_user_input()
        else:
            self._serve_404()
    
    def _serve_homepage(self):
        """服务主页"""
        response = {
            "name": "Interactive SSE MCP Server",
            "version": "1.0.0",
            "description": "支持用户交互的SSE服务器",
            "endpoints": {
                "stream": "/stream?session_id=<id>",
                "send": "/send (POST)",
                "demo": "/demo",
                "health": "/health"
            },
            "active_sessions": len(session_manager.sessions),
            "timestamp": datetime.now().isoformat()
        }
        self._send_json_response(response)
    
    def _serve_health(self):
        """健康检查"""
        response = {
            "status": "healthy",
            "active_sessions": len(session_manager.sessions),
            "timestamp": datetime.now().isoformat()
        }
        self._send_json_response(response)

    def _serve_demo_page(self):
        """服务演示页面"""
        html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式SSE演示</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 10px 0; }
        .messages { height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; }
        .message { margin: 5px 0; padding: 8px; border-radius: 4px; }
        .welcome { background: #e3f2fd; }
        .result { background: #e8f5e8; }
        .error { background: #ffebee; }
        .info { background: #fff3e0; }
        .heartbeat { background: #f3e5f5; font-size: 0.8em; opacity: 0.7; }
        .input-area { display: flex; gap: 10px; margin-top: 10px; }
        input[type="text"] { flex: 1; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        button { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .status { padding: 10px; background: #f8f9fa; border-radius: 4px; margin-bottom: 10px; }
    </style>
</head>
<body>
    <h1>🌟 交互式SSE演示</h1>

    <div class="status">
        <strong>连接状态:</strong> <span id="status">未连接</span><br>
        <strong>会话ID:</strong> <span id="sessionId">无</span>
    </div>

    <div class="container">
        <h3>📨 消息区域</h3>
        <div id="messages" class="messages"></div>

        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入消息或命令（如 /help）" disabled>
            <button onclick="sendMessage()" id="sendBtn" disabled>发送</button>
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开</button>
        </div>
    </div>

    <div class="container">
        <h3>💡 使用说明</h3>
        <ul>
            <li><strong>/help</strong> - 显示所有可用命令</li>
            <li><strong>/reverse 文本</strong> - 反转指定文本</li>
            <li><strong>/upper 文本</strong> - 转换为大写</li>
            <li><strong>/count</strong> - 显示消息计数</li>
            <li><strong>/session</strong> - 显示会话信息</li>
            <li><strong>普通文本</strong> - 自动反转</li>
        </ul>
    </div>

    <script>
        let eventSource = null;
        let sessionId = null;

        function connect() {
            if (eventSource) {
                eventSource.close();
            }

            sessionId = Math.random().toString(36).substr(2, 8);
            document.getElementById('sessionId').textContent = sessionId;

            const url = `/stream?session_id=${sessionId}`;
            eventSource = new EventSource(url);

            eventSource.onopen = function() {
                updateStatus('已连接', 'green');
                document.getElementById('messageInput').disabled = false;
                document.getElementById('sendBtn').disabled = false;
            };

            eventSource.onerror = function() {
                updateStatus('连接错误', 'red');
            };

            eventSource.addEventListener('welcome', function(event) {
                const data = JSON.parse(event.data);
                addMessage('welcome', `🎉 ${data.message}`);
            });

            eventSource.addEventListener('response', function(event) {
                const data = JSON.parse(event.data);
                displayResponse(data);
            });

            eventSource.addEventListener('heartbeat', function(event) {
                addMessage('heartbeat', `💓 心跳`);
            });
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || !sessionId) return;

            addMessage('user', `👤 你: ${message}`);

            fetch('/send', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ session_id: sessionId, message: message })
            });

            input.value = '';
        }

        function displayResponse(data) {
            let message = '';
            switch(data.type) {
                case 'result':
                    message = `🔄 ${data.operation}: "${data.input}" → "${data.output}"`;
                    break;
                case 'error':
                    message = `❌ ${data.message}`;
                    break;
                case 'help':
                    message = `📖 ${data.message}`;
                    break;
                case 'info':
                    message = `ℹ️ ${data.message}`;
                    break;
                default:
                    message = JSON.stringify(data);
            }
            addMessage(data.type, message);
        }

        function addMessage(type, content) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(status, color) {
            document.getElementById('status').textContent = status;
            document.getElementById('status').style.color = color;
        }

        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage();
        });

        window.onload = connect;
    </script>
</body>
</html>"""

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))

    def _serve_sse_stream(self, session_id: str):
        """提供SSE数据流"""
        self.send_response(200)
        self.send_header('Content-type', 'text/event-stream')
        self.send_header('Cache-Control', 'no-cache')
        self.send_header('Connection', 'keep-alive')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # 发送连接确认
        welcome_msg = {
            "type": "welcome",
            "session_id": session_id,
            "message": f"欢迎！会话ID: {session_id}",
            "instructions": "发送POST请求到 /send 来与服务器交互",
            "timestamp": datetime.now().isoformat()
        }
        self._send_sse_event("welcome", welcome_msg)
        
        try:
            # 持续监听消息
            while True:
                session_manager.update_activity(session_id)
                message = session_manager.get_message(session_id, timeout=5.0)
                
                if message:
                    self._send_sse_event("response", message)
                else:
                    # 发送心跳
                    heartbeat = {
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat()
                    }
                    self._send_sse_event("heartbeat", heartbeat)
                
        except Exception as e:
            logger.error(f"SSE流错误: {e}")
    
    def _handle_user_input(self):
        """处理用户输入"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            session_id = data.get("session_id")
            user_input = data.get("message", "")
            
            if not session_id or session_id not in session_manager.sessions:
                self._send_json_response({"error": "无效的会话ID"}, 400)
                return
            
            # 处理消息
            response = MessageProcessor.process_message(session_id, user_input)
            
            # 将响应添加到会话队列
            session_manager.add_message(session_id, response)
            
            # 返回确认
            self._send_json_response({"status": "received", "session_id": session_id})
            
        except Exception as e:
            logger.error(f"处理用户输入错误: {e}")
            self._send_json_response({"error": "处理请求失败"}, 500)
    
    def _send_sse_event(self, event_type: str, data: Dict[str, Any]):
        """发送SSE事件"""
        event_data = f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"
        self.wfile.write(event_data.encode('utf-8'))
        self.wfile.flush()
    
    def _send_json_response(self, data: Dict[str, Any], status_code: int = 200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
    
    def _serve_404(self):
        """404错误页面"""
        response = {"error": "Not Found", "path": self.path}
        self._send_json_response(response, 404)
    
    def log_message(self, format, *args):
        """自定义日志"""
        logger.info(f"{self.address_string()} - {format % args}")

def run_server():
    """启动服务器"""
    PORT = 8001  # 使用不同端口避免冲突
    
    # 启动会话清理线程
    def cleanup_sessions():
        while True:
            time.sleep(300)  # 每5分钟清理一次
            session_manager.cleanup_old_sessions()
    
    cleanup_thread = threading.Thread(target=cleanup_sessions, daemon=True)
    cleanup_thread.start()
    
    try:
        with socketserver.TCPServer(("", PORT), InteractiveSSEHandler) as httpd:
            logger.info(f"🚀 交互式SSE服务器启动成功！")
            logger.info(f"📡 服务器地址: http://localhost:{PORT}")
            logger.info(f"🌐 可用端点:")
            logger.info(f"   - http://localhost:{PORT}/ (服务器信息)")
            logger.info(f"   - http://localhost:{PORT}/stream?session_id=<id> (SSE流)")
            logger.info(f"   - http://localhost:{PORT}/send (POST用户输入)")
            logger.info(f"   - http://localhost:{PORT}/health (健康检查)")
            logger.info(f"💡 这是一个支持用户交互的SSE服务器")
            logger.info(f"⏹️  按 Ctrl+C 停止服务器")
            httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🌟 交互式 SSE MCP Server")
    print("=" * 60)
    run_server()
